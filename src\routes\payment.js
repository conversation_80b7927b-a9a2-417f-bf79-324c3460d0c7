const express = require('express');
const router = express.Router();
const PaymentController = require('../controllers/PaymentController');
const { authenticate } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// 创建充值订单（需要认证）
router.post('/orders',
  authenticate,
  validate(schemas.createOrder),
  PaymentController.createOrder
);

// 查询订单状态（需要认证）
router.get('/orders/:order_sn',
  authenticate,
  validate(schemas.orderSnParam, 'params'),
  PaymentController.getOrderStatus
);

// 取消订单（需要认证）
router.put('/orders/:order_sn/cancel',
  authenticate,
  validate(schemas.orderSnParam, 'params'),
  PaymentController.cancelOrder
);

// 重新支付订单（需要认证）
router.post('/orders/:order_sn/repay',
  authenticate,
  validate(schemas.orderSnParam, 'params'),
  PaymentController.repayOrder
);

// 获取支付方式列表（需要认证）
router.get('/methods',
  authenticate,
  PaymentController.getPaymentMethods
);

// 支付宝支付回调（公开接口）
router.post('/notify/alipay',
  PaymentController.alipayNotify
);

// 微信支付回调（公开接口）
router.post('/notify/wechat_pay',
  PaymentController.wechatNotify
);

module.exports = router;
