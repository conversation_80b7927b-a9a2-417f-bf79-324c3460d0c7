const { pool } = require('../config/database');

/**
 * 数据模型基类
 */
class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
    this.pool = pool;
  }

  /**
   * 执行SQL查询
   * @param {String} sql - SQL语句
   * @param {Array} params - 参数
   * @returns {Array} 查询结果
   */
  async query(sql, params = []) {
    try {
      const [rows] = await this.pool.execute(sql, params);
      return rows;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  /**
   * 根据ID查找记录
   * @param {Number} id - 记录ID
   * @returns {Object|null} 记录对象或null
   */
  async findById(id) {
    const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    const rows = await this.query(sql, [id]);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 根据条件查找记录
   * @param {Object} conditions - 查询条件
   * @returns {Array} 记录数组
   */
  async findWhere(conditions = {}) {
    const keys = Object.keys(conditions);
    if (keys.length === 0) {
      const sql = `SELECT * FROM ${this.tableName}`;
      return await this.query(sql);
    }

    const whereClause = keys.map(key => `${key} = ?`).join(' AND ');
    const values = keys.map(key => conditions[key]);
    
    const sql = `SELECT * FROM ${this.tableName} WHERE ${whereClause}`;
    return await this.query(sql, values);
  }

  /**
   * 根据条件查找单条记录
   * @param {Object} conditions - 查询条件
   * @returns {Object|null} 记录对象或null
   */
  async findOneWhere(conditions) {
    const rows = await this.findWhere(conditions);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 创建记录
   * @param {Object} data - 数据对象
   * @returns {Object} 创建的记录
   */
  async create(data) {
    const keys = Object.keys(data);
    const values = keys.map(key => data[key]);
    const placeholders = keys.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`;
    const result = await this.query(sql, values);
    
    return await this.findById(result.insertId);
  }

  /**
   * 更新记录
   * @param {Number} id - 记录ID
   * @param {Object} data - 更新数据
   * @returns {Object|null} 更新后的记录
   */
  async update(id, data) {
    const keys = Object.keys(data);
    const values = keys.map(key => data[key]);
    const setClause = keys.map(key => `${key} = ?`).join(', ');
    
    const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`;
    await this.query(sql, [...values, id]);
    
    return await this.findById(id);
  }

  /**
   * 删除记录
   * @param {Number} id - 记录ID
   * @returns {Boolean} 是否删除成功
   */
  async delete(id) {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
    const result = await this.query(sql, [id]);
    return result.affectedRows > 0;
  }

  /**
   * 统计记录数
   * @param {Object} conditions - 查询条件
   * @returns {Number} 记录数
   */
  async count(conditions = {}) {
    const keys = Object.keys(conditions);
    let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    let values = [];

    if (keys.length > 0) {
      const whereClause = keys.map(key => `${key} = ?`).join(' AND ');
      values = keys.map(key => conditions[key]);
      sql += ` WHERE ${whereClause}`;
    }

    const rows = await this.query(sql, values);
    return rows[0].count;
  }

  /**
   * 分页查询
   * @param {Object} options - 查询选项
   * @returns {Object} 分页结果
   */
  async paginate(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      conditions = {},
      orderBy = 'id',
      orderDirection = 'DESC'
    } = options;

    const offset = (page - 1) * pageSize;
    const keys = Object.keys(conditions);
    let sql = `SELECT * FROM ${this.tableName}`;
    let countSql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    let values = [];

    if (keys.length > 0) {
      const whereClause = keys.map(key => `${key} = ?`).join(' AND ');
      values = keys.map(key => conditions[key]);
      sql += ` WHERE ${whereClause}`;
      countSql += ` WHERE ${whereClause}`;
    }

    sql += ` ORDER BY ${orderBy} ${orderDirection} LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}`;

    const [list, countResult] = await Promise.all([
      this.query(sql, values),
      this.query(countSql, values)
    ]);

    return {
      list,
      total: countResult[0].count,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };
  }
}

module.exports = BaseModel;
