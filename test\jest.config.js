module.exports = {
  // 测试环境
  testEnvironment: 'node',
  
  // 测试文件匹配模式
  testMatch: [
    '**/test/**/*.test.js'
  ],
  
  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/test/jest.setup.js'],
  
  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/app.js', // 排除主应用文件
    '!**/node_modules/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  
  // 超时设置
  testTimeout: 30000,
  
  // 详细输出
  verbose: true,
  
  // 测试完成后不退出
  forceExit: true,
  
  // 检测打开的句柄
  detectOpenHandles: true,
  
  // 并发运行
  maxWorkers: 1, // 数据库测试建议单线程运行
  
  // 全局变量
  globals: {
    'process.env.NODE_ENV': 'test'
  }
};
