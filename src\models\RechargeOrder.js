const BaseModel = require('./BaseModel');

class RechargeOrder extends BaseModel {
  constructor() {
    super('recharge_orders');
  }

  /**
   * 生成订单号
   * @returns {String} 订单号
   */
  generateOrderSn() {
    const now = new Date();
    const timestamp = now.getTime();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `RO${timestamp}${random}`;
  }

  /**
   * 创建充值订单
   * @param {Object} orderData - 订单数据
   * @returns {Object} 创建的订单
   */
  async createOrder(orderData) {
    const {
      user_id,
      package_id,
      amount_paid,
      balance_granted,
      payment_method
    } = orderData;

    const order_sn = this.generateOrderSn();

    return await this.create({
      order_sn,
      user_id,
      package_id,
      amount_paid,
      balance_granted,
      payment_method,
      status: 'pending'
    });
  }

  /**
   * 根据订单号查找订单
   * @param {String} orderSn - 订单号
   * @returns {Object|null} 订单信息
   */
  async findByOrderSn(orderSn) {
    return await this.findOneWhere({ order_sn: orderSn });
  }

  /**
   * 更新订单状态
   * @param {String} orderSn - 订单号
   * @param {String} status - 新状态
   * @param {Object} extraData - 额外数据
   * @returns {Object|null} 更新后的订单
   */
  async updateOrderStatus(orderSn, status, extraData = {}) {
    const order = await this.findByOrderSn(orderSn);
    if (!order) return null;

    const updateData = { status, ...extraData };
    
    // 如果是完成状态，记录支付时间
    if (status === 'completed' && !extraData.paid_at) {
      updateData.paid_at = new Date();
    }

    return await this.update(order.id, updateData);
  }

  /**
   * 获取用户的充值订单列表
   * @param {Number} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Object} 分页结果
   */
  async getUserOrders(userId, options = {}) {
    const {
      page = 1,
      pageSize = 10,
      status = null
    } = options;

    const offset = (page - 1) * pageSize;
    let sql = `
      SELECT 
        ro.order_sn,
        ro.amount_paid,
        ro.balance_granted,
        ro.payment_method,
        ro.status,
        ro.created_at,
        ro.paid_at,
        rp.name as package_name,
        rp.description as package_description
      FROM ${this.tableName} ro
      LEFT JOIN recharge_packages rp ON ro.package_id = rp.id
      WHERE ro.user_id = ?
    `;
    const params = [userId];

    if (status) {
      sql += ` AND ro.status = ?`;
      params.push(status);
    }

    sql += ` ORDER BY ro.created_at DESC LIMIT ? OFFSET ?`;
    params.push(pageSize, offset);

    const countSql = `
      SELECT COUNT(*) as count 
      FROM ${this.tableName} 
      WHERE user_id = ?${status ? ' AND status = ?' : ''}
    `;
    const countParams = status ? [userId, status] : [userId];

    const [list, countResult] = await Promise.all([
      this.query(sql, params),
      this.query(countSql, countParams)
    ]);

    return {
      list,
      total: countResult[0].count,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };
  }

  /**
   * 获取订单详情（包含套餐信息）
   * @param {String} orderSn - 订单号
   * @param {Number} userId - 用户ID（用于权限验证）
   * @returns {Object|null} 订单详情
   */
  async getOrderDetail(orderSn, userId = null) {
    let sql = `
      SELECT 
        ro.*,
        rp.name as package_name,
        rp.description as package_description,
        u.uid as user_uid
      FROM ${this.tableName} ro
      LEFT JOIN recharge_packages rp ON ro.package_id = rp.id
      LEFT JOIN users u ON ro.user_id = u.id
      WHERE ro.order_sn = ?
    `;
    const params = [orderSn];

    if (userId) {
      sql += ` AND ro.user_id = ?`;
      params.push(userId);
    }

    const rows = await this.query(sql, params);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 获取用户充值统计
   * @param {Number} userId - 用户ID
   * @returns {Object} 充值统计
   */
  async getUserRechargeStats(userId) {
    const sql = `
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
        SUM(CASE WHEN status = 'completed' THEN amount_paid ELSE 0 END) as total_paid,
        SUM(CASE WHEN status = 'completed' THEN balance_granted ELSE 0 END) as total_balance_granted,
        MAX(CASE WHEN status = 'completed' THEN paid_at END) as last_recharge_time
      FROM ${this.tableName}
      WHERE user_id = ?
    `;

    const rows = await this.query(sql, [userId]);
    return rows[0];
  }

  /**
   * 获取待处理的订单
   * @param {Number} limit - 限制数量
   * @returns {Array} 待处理订单列表
   */
  async getPendingOrders(limit = 100) {
    const sql = `
      SELECT 
        ro.*,
        u.uid as user_uid,
        rp.name as package_name
      FROM ${this.tableName} ro
      LEFT JOIN users u ON ro.user_id = u.id
      LEFT JOIN recharge_packages rp ON ro.package_id = rp.id
      WHERE ro.status = 'pending'
      ORDER BY ro.created_at ASC
      LIMIT ?
    `;

    return await this.query(sql, [limit]);
  }

  /**
   * 根据外部交易ID查找订单
   * @param {String} externalTxnId - 外部交易ID
   * @returns {Object|null} 订单信息
   */
  async findByExternalTxnId(externalTxnId) {
    return await this.findOneWhere({ external_txn_id: externalTxnId });
  }

  /**
   * 取消超时的待支付订单
   * @param {Number} timeoutMinutes - 超时分钟数，默认30分钟
   * @returns {Number} 取消的订单数量
   */
  async cancelTimeoutOrders(timeoutMinutes = 30) {
    const sql = `
      UPDATE ${this.tableName} 
      SET status = 'cancelled' 
      WHERE status = 'pending' 
        AND created_at < DATE_SUB(NOW(), INTERVAL ? MINUTE)
    `;

    const result = await this.query(sql, [timeoutMinutes]);
    return result.affectedRows;
  }
}

module.exports = new RechargeOrder();
