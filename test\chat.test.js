const { app, request, TEST_USERS, testUtils } = require('./setup');

describe('聊天接口测试', () => {
  let characterId;

  beforeAll(async () => {
    // 获取可用的AI角色
    const charactersRes = await request(app)
      .get('/api/v1/ai-characters')
      .expect(200);

    if (charactersRes.body.data.list.length > 0) {
      characterId = charactersRes.body.data.list[0].id;
    } else {
      // 如果没有角色，跳过聊天测试
      console.warn('没有可用的AI角色，跳过聊天测试');
    }
  });

  beforeEach(async () => {
    await testUtils.createUserAndGetToken(TEST_USERS.user1);
  });

  afterEach(async () => {
    await testUtils.cleanupTestData();
  });

  describe('POST /api/v1/chat/messages - 发送消息', () => {
    test('发送消息成功', async () => {
      if (!characterId) {
        console.warn('跳过发送消息测试：没有可用角色');
        return;
      }

      const messageData = {
        character_id: characterId,
        content: '你好，我想聊聊天',
        stream: false
      };

      const res = await request(app)
        .post('/api/v1/chat/messages')
        .set(testUtils.getAuthHeader())
        .send(messageData)
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('reply_message');
      expect(res.body.data).toHaveProperty('token_usage');
      expect(res.body.data).toHaveProperty('balance_change');
      expect(res.body.data).toHaveProperty('current_balance');

      expect(res.body.data.reply_message).toHaveProperty('id');
      expect(res.body.data.reply_message).toHaveProperty('content');
      expect(res.body.data.reply_message.sender_type).toBe('ai');
    });

    test('发送消息给不存在的角色', async () => {
      const messageData = {
        character_id: 99999,
        content: '你好',
        stream: false
      };

      const res = await request(app)
        .post('/api/v1/chat/messages')
        .set(testUtils.getAuthHeader())
        .send(messageData)
        .expect(404);

      expect(res.body.code).toBe(40401);
      expect(res.body.message).toContain('不存在');
    });

    test('发送空消息', async () => {
      if (!characterId) return;

      const messageData = {
        character_id: characterId,
        content: '',
        stream: false
      };

      const res = await request(app)
        .post('/api/v1/chat/messages')
        .set(testUtils.getAuthHeader())
        .send(messageData)
        .expect(400);

      expect(res.body.code).toBe(40001);
    });

    test('消息内容过长', async () => {
      if (!characterId) return;

      const longContent = 'a'.repeat(2001); // 超过2000字符限制
      const messageData = {
        character_id: characterId,
        content: longContent,
        stream: false
      };

      const res = await request(app)
        .post('/api/v1/chat/messages')
        .set(testUtils.getAuthHeader())
        .send(messageData)
        .expect(400);

      expect(res.body.code).toBe(40001);
    });

    test('未认证用户发送消息', async () => {
      if (!characterId) return;

      const messageData = {
        character_id: characterId,
        content: '你好',
        stream: false
      };

      const res = await request(app)
        .post('/api/v1/chat/messages')
        .send(messageData)
        .expect(401);

      expect(res.body.code).toBe(40101);
    });
  });

  describe('GET /api/v1/chat/sessions/:character_id/messages - 获取历史消息', () => {
    test('获取历史消息成功', async () => {
      if (!characterId) return;

      // 先发送一条消息创建会话
      await request(app)
        .post('/api/v1/chat/messages')
        .set(testUtils.getAuthHeader())
        .send({
          character_id: characterId,
          content: '测试消息',
          stream: false
        })
        .expect(200);

      // 获取历史消息
      const res = await request(app)
        .get(`/api/v1/chat/sessions/${characterId}/messages`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('messages');
      expect(res.body.data).toHaveProperty('session_id');
      expect(res.body.data).toHaveProperty('has_more');
      expect(Array.isArray(res.body.data.messages)).toBe(true);
    });

    test('获取不存在角色的消息', async () => {
      const res = await request(app)
        .get('/api/v1/chat/sessions/99999/messages')
        .set(testUtils.getAuthHeader())
        .expect(404);

      expect(res.body.code).toBe(40401);
    });

    test('分页获取消息', async () => {
      if (!characterId) return;

      const res = await request(app)
        .get(`/api/v1/chat/sessions/${characterId}/messages?limit=10`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.data.messages.length).toBeLessThanOrEqual(10);
    });
  });

  describe('GET /api/v1/chat/sessions - 获取会话列表', () => {
    test('获取会话列表成功', async () => {
      const res = await request(app)
        .get('/api/v1/chat/sessions')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('list');
      expect(res.body.data).toHaveProperty('total');
      expect(res.body.data).toHaveProperty('page');
      expect(res.body.data).toHaveProperty('pageSize');
      expect(Array.isArray(res.body.data.list)).toBe(true);
    });

    test('分页获取会话列表', async () => {
      const res = await request(app)
        .get('/api/v1/chat/sessions?page=1&pageSize=5')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.data.page).toBe(1);
      expect(res.body.data.pageSize).toBe(5);
    });
  });

  describe('GET /api/v1/chat/sessions/stats - 获取会话统计', () => {
    test('获取会话统计成功', async () => {
      const res = await request(app)
        .get('/api/v1/chat/sessions/stats')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('total_sessions');
      expect(res.body.data).toHaveProperty('active_sessions');
      expect(typeof res.body.data.total_sessions).toBe('number');
      expect(typeof res.body.data.active_sessions).toBe('number');
    });
  });

  describe('DELETE /api/v1/chat/sessions/:session_id - 删除会话', () => {
    test('删除不存在的会话', async () => {
      const res = await request(app)
        .delete('/api/v1/chat/sessions/99999')
        .set(testUtils.getAuthHeader())
        .expect(404);

      expect(res.body.code).toBe(40401);
      expect(res.body.message).toContain('不存在');
    });

    test('无效的会话ID', async () => {
      const res = await request(app)
        .delete('/api/v1/chat/sessions/invalid')
        .set(testUtils.getAuthHeader())
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/chat/sessions/:character_id/messages/search - 搜索消息', () => {
    test('搜索消息成功', async () => {
      if (!characterId) return;

      const res = await request(app)
        .get(`/api/v1/chat/sessions/${characterId}/messages/search?keyword=测试`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('messages');
      expect(Array.isArray(res.body.data.messages)).toBe(true);
    });

    test('空关键词搜索', async () => {
      if (!characterId) return;

      const res = await request(app)
        .get(`/api/v1/chat/sessions/${characterId}/messages/search?keyword=`)
        .set(testUtils.getAuthHeader())
        .expect(400);

      expect(res.body.code).toBe(40001);
    });

    test('缺少关键词参数', async () => {
      if (!characterId) return;

      const res = await request(app)
        .get(`/api/v1/chat/sessions/${characterId}/messages/search`)
        .set(testUtils.getAuthHeader())
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });
});
