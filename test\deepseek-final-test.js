const http = require('http');

console.log('🤖 DeepSeek V3 Fast 最终测试...\n');

// HTTP请求工具
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(body);
          resolve({ status: res.statusCode, data: json });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      console.log('请求错误:', err.message);
      resolve({ status: 0, data: null });
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testDeepSeekIntegration() {
  console.log('📋 配置信息:');
  console.log('   API地址: https://www.sophnet.com/api/open-apis/v1');
  console.log('   模型: DeepSeek-V3-Fast');
  console.log('   API密钥: YWos2nuJ7k9-Mogh5AwDp_BxtQy_UonZSHbulcGtoYTQFLuvY9ruqvfOdM4qTDoREN0RLPMj7X5z_bK4H0vbzQ');
  console.log('   定价: ¥2/M输入 + ¥8/M输出 (限时五折)\n');

  let token = null;

  // 1. 注册用户获取token
  console.log('🔐 注册测试用户...');
  const registerResult = await makeRequest('POST', '/api/v1/auth/register', {
    email: `deepseek_final_${Date.now()}@example.com`,
    password: 'password123'
  });

  if (registerResult.status === 200 && registerResult.data.code === 0) {
    token = registerResult.data.data.access_token;
    console.log('✅ 用户注册成功，获得token');
  } else {
    console.log('❌ 用户注册失败:', registerResult.data);
    return;
  }

  // 2. 测试AI健康检查
  console.log('🏥 测试AI服务健康检查...');
  const healthResult = await makeRequest('GET', '/api/v1/ai/health');
  if (healthResult.status === 200) {
    console.log('✅ AI服务健康检查通过');
    console.log('   默认模型:', healthResult.data.data?.config?.default_model);
  } else {
    console.log('❌ AI服务健康检查失败');
  }

  // 3. 直接测试DeepSeek对话
  console.log('💬 测试DeepSeek V3 Fast对话...');
  const chatResult = await makeRequest('POST', '/api/v1/chat/messages', {
    character_id: 1,
    content: '你好，请用一句话介绍你自己',
    stream: false,
    model: 'DeepSeek-V3-Fast'
  }, {
    'Authorization': `Bearer ${token}`
  });

  if (chatResult.status === 200 && chatResult.data.code === 0) {
    console.log('✅ DeepSeek对话成功！');
    console.log('   AI回复:', chatResult.data.data.reply_message.content);
    console.log('   Token使用:');
    console.log('     输入Token:', chatResult.data.data.token_usage.promptTokens);
    console.log('     输出Token:', chatResult.data.data.token_usage.completionTokens);
    console.log('     总Token:', chatResult.data.data.token_usage.totalTokens);
    console.log('   费用信息:');
    console.log('     API成本:', chatResult.data.data.cost.apiCost, chatResult.data.data.cost.currency);
    console.log('     用户成本:', chatResult.data.data.cost.userCost, chatResult.data.data.cost.currency);
    console.log('     余额变化:', chatResult.data.data.balance_change);
    console.log('     当前余额:', chatResult.data.data.current_balance);
    console.log('   响应时间:', chatResult.data.data.response_time + 'ms');
  } else {
    console.log('❌ DeepSeek对话失败');
    console.log('状态码:', chatResult.status);
    console.log('响应:', chatResult.data);
    
    // 如果是认证问题，尝试重新获取用户信息
    if (chatResult.status === 401) {
      console.log('🔍 检查用户认证状态...');
      const profileResult = await makeRequest('GET', '/api/v1/user/profile', null, {
        'Authorization': `Bearer ${token}`
      });
      console.log('用户信息:', profileResult.data);
    }
  }

  // 4. 测试流式响应（如果普通对话成功）
  if (chatResult.status === 200 && chatResult.data.code === 0) {
    console.log('🌊 测试流式响应...');
    
    // 这里我们不能直接测试流式响应，因为需要特殊的SSE处理
    // 但我们可以测试流式接口是否接受请求
    const streamTestResult = await makeRequest('POST', '/api/v1/chat/messages', {
      character_id: 1,
      content: '请说"测试成功"',
      stream: true,
      model: 'DeepSeek-V3-Fast'
    }, {
      'Authorization': `Bearer ${token}`
    });

    if (streamTestResult.status === 200) {
      console.log('✅ 流式接口响应正常');
    } else {
      console.log('❌ 流式接口测试失败:', streamTestResult.status);
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('🎉 DeepSeek V3 Fast 集成测试完成！');
  console.log('='.repeat(60));
  
  if (chatResult.status === 200 && chatResult.data.code === 0) {
    console.log('✅ 集成成功！系统已可以使用DeepSeek V3 Fast进行AI对话');
    console.log('\n🚀 系统特性:');
    console.log('• 🧠 DeepSeek V3 Fast (671B参数，37B激活)');
    console.log('• ⚡ 高速响应，优化推理性能');
    console.log('• 💰 成本优化 (¥2/M输入 + ¥8/M输出)');
    console.log('• 📝 32K上下文长度');
    console.log('• 🔄 支持流式和非流式响应');
    console.log('• 💳 精确Token计费和余额管理');
    console.log('\n📋 可用接口:');
    console.log('• POST /api/v1/chat/messages - AI对话 (支持model参数)');
    console.log('• GET  /api/v1/ai/health - AI服务状态');
    console.log('• GET  /api/v1/ai/models - 支持的模型列表');
    console.log('• GET  /api/v1/ai/pricing - 模型定价信息');
  } else {
    console.log('❌ 集成失败，请检查:');
    console.log('• API密钥是否正确');
    console.log('• 网络连接是否正常');
    console.log('• 模型名称是否匹配');
    console.log('• 服务器配置是否正确');
  }
}

testDeepSeekIntegration().catch(console.error);
