-- 用户记忆系统相关表

-- 用户偏好表
CREATE TABLE IF NOT EXISTS user_preferences (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  preference_key VARCHAR(100) NOT NULL,
  preference_value TEXT,
  preference_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  weight DECIMAL(3,2) DEFAULT 1.00 COMMENT '偏好权重 0-1',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_preferences (user_id, preference_key),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户偏好表';

-- 用户记忆片段表
CREATE TABLE IF NOT EXISTS user_memories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  character_id INT,
  memory_type ENUM('conversation', 'preference', 'emotion', 'event', 'fact') NOT NULL,
  memory_content TEXT NOT NULL,
  memory_summary VARCHAR(500) COMMENT '记忆摘要',
  importance_score DECIMAL(3,2) DEFAULT 0.50 COMMENT '重要性评分 0-1',
  emotional_tone ENUM('positive', 'negative', 'neutral', 'mixed') DEFAULT 'neutral',
  tags JSON COMMENT '记忆标签',
  related_message_id INT COMMENT '关联的消息ID',
  expires_at TIMESTAMP NULL COMMENT '过期时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_memories (user_id, character_id),
  INDEX idx_memory_type (memory_type),
  INDEX idx_importance (importance_score DESC),
  INDEX idx_created_at (created_at DESC),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (character_id) REFERENCES ai_characters(id) ON DELETE SET NULL,
  FOREIGN KEY (related_message_id) REFERENCES chat_messages(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户记忆片段表';

-- 角色状态表
CREATE TABLE IF NOT EXISTS character_states (
  id INT PRIMARY KEY AUTO_INCREMENT,
  character_id INT NOT NULL,
  user_id INT NOT NULL,
  state_type ENUM('mood', 'energy', 'relationship', 'knowledge', 'personality') NOT NULL,
  state_value JSON NOT NULL COMMENT '状态值',
  last_interaction_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_character_user_state (character_id, user_id, state_type),
  INDEX idx_character_states (character_id, user_id),
  FOREIGN KEY (character_id) REFERENCES ai_characters(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色状态表';

-- 用户行为分析表
CREATE TABLE IF NOT EXISTS user_behavior_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  behavior_type ENUM('login', 'chat', 'recharge', 'character_switch', 'setting_change') NOT NULL,
  behavior_data JSON COMMENT '行为数据',
  session_id VARCHAR(100) COMMENT '会话ID',
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_behavior (user_id, behavior_type),
  INDEX idx_created_at (created_at DESC),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为分析表';

-- 系统监控指标表
CREATE TABLE IF NOT EXISTS system_metrics (
  id INT PRIMARY KEY AUTO_INCREMENT,
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(15,4) NOT NULL,
  metric_unit VARCHAR(20) DEFAULT '',
  metric_tags JSON COMMENT '指标标签',
  recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_metric_name (metric_name),
  INDEX idx_recorded_at (recorded_at DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统监控指标表';

-- 系统告警表
CREATE TABLE IF NOT EXISTS system_alerts (
  id INT PRIMARY KEY AUTO_INCREMENT,
  alert_type ENUM('error', 'warning', 'info', 'critical') NOT NULL,
  alert_title VARCHAR(200) NOT NULL,
  alert_message TEXT,
  alert_data JSON COMMENT '告警数据',
  status ENUM('active', 'acknowledged', 'resolved') DEFAULT 'active',
  severity INT DEFAULT 1 COMMENT '严重程度 1-5',
  source VARCHAR(100) COMMENT '告警源',
  resolved_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_alert_type (alert_type),
  INDEX idx_status (status),
  INDEX idx_severity (severity DESC),
  INDEX idx_created_at (created_at DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统告警表';

-- 用户推荐表
CREATE TABLE IF NOT EXISTS user_recommendations (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  recommendation_type ENUM('character', 'topic', 'feature', 'package') NOT NULL,
  recommendation_data JSON NOT NULL COMMENT '推荐数据',
  confidence_score DECIMAL(3,2) DEFAULT 0.50 COMMENT '置信度 0-1',
  status ENUM('pending', 'shown', 'clicked', 'dismissed') DEFAULT 'pending',
  expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_recommendations (user_id, recommendation_type),
  INDEX idx_status (status),
  INDEX idx_confidence (confidence_score DESC),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户推荐表';
