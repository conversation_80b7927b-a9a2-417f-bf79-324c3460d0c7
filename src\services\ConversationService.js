const OpenAIService = require('./OpenAIService');
const AICharacter = require('../models/AICharacter');
const ChatSession = require('../models/ChatSession');
const ChatMessage = require('../models/ChatMessage');
const ConsumptionLog = require('../models/ConsumptionLog');
const User = require('../models/User');
const { aiConfig } = require('../config/ai');

class ConversationService {
  constructor() {
    this.openAI = OpenAIService;
  }

  /**
   * 处理用户消息并生成AI回复
   * @param {Number} userId - 用户ID
   * @param {Number} characterId - AI角色ID
   * @param {String} content - 用户消息内容
   * @param {Object} options - 选项
   * @returns {Object} 对话结果
   */
  async processMessage(userId, characterId, content, options = {}) {
    const { stream = false, model = null } = options;

    try {
      // 1. 验证用户和角色
      const [user, character] = await Promise.all([
        User.findById(userId),
        AICharacter.getCharacterDetail(characterId)
      ]);

      if (!user) {
        throw new Error('User not found');
      }

      if (!character || character.status !== 'online') {
        throw new Error('Character not available');
      }

      // 2. 内容安全检查
      await this.validateContent(content);

      // 3. 检查用户余额
      await this.checkUserBalance(user, model || aiConfig.openai.defaultModel);

      // 4. 获取或创建会话
      const session = await ChatSession.getOrCreateSession(userId, characterId);

      // 5. 保存用户消息
      const userMessage = await ChatMessage.createUserMessage(session.id, content);

      // 6. 构建对话上下文
      const messages = await this.buildConversationContext(session.id, character, content);

      // 7. 调用AI服务
      const aiResponse = await this.openAI.createChatCompletion(messages, {
        model: model || aiConfig.openai.defaultModel,
        stream,
        user: userId.toString()
      });

      // 8. 处理AI响应
      if (stream) {
        return this.handleStreamResponse(aiResponse, session, userMessage, user);
      } else {
        return await this.handleNormalResponse(aiResponse, session, userMessage, user);
      }

    } catch (error) {
      console.error('Conversation processing error:', error);
      throw error;
    }
  }

  /**
   * 构建对话上下文
   */
  async buildConversationContext(sessionId, character, currentMessage) {
    // 获取历史消息
    const historyMessages = await ChatMessage.getRecentContext(
      sessionId, 
      aiConfig.conversation.maxContextLength - 1 // 减1为当前消息留位置
    );

    // 构建消息数组
    const messages = [
      // 系统提示词
      {
        role: 'system',
        content: character.system_prompt
      }
    ];

    // 添加历史对话
    historyMessages.forEach(msg => {
      messages.push({
        role: msg.sender_type === 'user' ? 'user' : 'assistant',
        content: msg.content
      });
    });

    // 添加当前用户消息
    messages.push({
      role: 'user',
      content: currentMessage
    });

    return messages;
  }

  /**
   * 处理普通响应
   */
  async handleNormalResponse(aiResponse, session, userMessage, user) {
    try {
      // 保存AI回复消息
      const aiMessage = await ChatMessage.createAIMessage(
        session.id,
        aiResponse.content,
        aiResponse.usage
      );

      // 计算并扣除费用
      const balanceChange = -parseFloat(aiResponse.cost.userCost);
      const updatedUser = await User.updateBalance(user.id, balanceChange);
      const newBalance = parseFloat(updatedUser.balance);

      // 记录消费日志
      await ConsumptionLog.createLog({
        user_id: user.id,
        message_id: aiMessage.id,
        balance_change: balanceChange,
        balance_after: newBalance,
        tokens_consumed: aiResponse.usage.totalTokens,
        description: `与AI角色对话消费`
      });

      // 更新角色热度
      await AICharacter.increasePopularity(session.character_id);

      // 更新Token使用计数
      OpenAIService.updateTokenCount(user.id, aiResponse.usage.totalTokens);

      return {
        success: true,
        reply_message: {
          id: aiMessage.id,
          sender_type: 'ai',
          content: aiMessage.content,
          created_at: aiMessage.created_at
        },
        token_usage: aiResponse.usage,
        cost: aiResponse.cost,
        balance_change: balanceChange.toFixed(4),
        current_balance: newBalance.toFixed(4),
        response_time: aiResponse.responseTime
      };

    } catch (error) {
      console.error('Error handling normal response:', error);
      throw error;
    }
  }

  /**
   * 处理流式响应
   */
  async handleStreamResponse(aiResponse, session, userMessage, user) {
    return {
      success: true,
      stream: true,
      session_id: session.id,
      user_message_id: userMessage.id,
      
      async *[Symbol.asyncIterator]() {
        let fullContent = '';
        let finalUsage = null;
        let finalCost = null;

        try {
          for await (const chunk of aiResponse) {
            if (chunk.type === 'content') {
              fullContent = chunk.accumulated;
              yield {
                type: 'content',
                content: chunk.content,
                accumulated: fullContent
              };
            } else if (chunk.type === 'done') {
              finalUsage = chunk.usage;
              finalCost = chunk.cost;

              // 保存AI回复消息
              const aiMessage = await ChatMessage.createAIMessage(
                session.id,
                fullContent,
                finalUsage
              );

              // 计算并扣除费用
              const balanceChange = -parseFloat(finalCost.userCost);
              const updatedUser = await User.updateBalance(user.id, balanceChange);
              const newBalance = parseFloat(updatedUser.balance);

              // 记录消费日志
              await ConsumptionLog.createLog({
                user_id: user.id,
                message_id: aiMessage.id,
                balance_change: balanceChange,
                balance_after: newBalance,
                tokens_consumed: finalUsage.totalTokens,
                description: `与AI角色对话消费`
              });

              // 更新角色热度
              await AICharacter.increasePopularity(session.character_id);

              // 更新Token使用计数
              OpenAIService.updateTokenCount(user.id, finalUsage.totalTokens);

              yield {
                type: 'done',
                message_id: aiMessage.id,
                token_usage: finalUsage,
                cost: finalCost,
                balance_change: balanceChange.toFixed(4),
                current_balance: newBalance.toFixed(4),
                response_time: chunk.responseTime,
                finish_reason: chunk.finishReason
              };
            } else if (chunk.type === 'error') {
              yield {
                type: 'error',
                error: chunk.error.message
              };
            }
          }
        } catch (error) {
          yield {
            type: 'error',
            error: error.message
          };
        }
      }
    };
  }

  /**
   * 验证消息内容
   */
  async validateContent(content) {
    // 检查长度
    if (content.length > aiConfig.security.maxInputLength) {
      throw new Error('Message too long');
    }

    // 检查敏感词
    if (aiConfig.security.enableContentModeration) {
      const bannedKeywords = aiConfig.security.bannedKeywords;
      for (const keyword of bannedKeywords) {
        if (content.includes(keyword)) {
          throw new Error('Content contains inappropriate material');
        }
      }
    }

    return true;
  }

  /**
   * 检查用户余额
   */
  async checkUserBalance(user, model) {
    const estimatedTokens = Math.min(
      Math.ceil(user.content?.length / 4) + aiConfig.conversation.maxTokens,
      4000
    );
    
    const pricing = aiConfig.openai.modelPricing[model] || aiConfig.openai.modelPricing['gpt-3.5-turbo'];
    const estimatedCost = (estimatedTokens / 1000) * Math.max(pricing.input, pricing.output);
    const userCost = estimatedCost * aiConfig.billing.usdToCnyRate * (1 + aiConfig.billing.serviceFeeRate);

    if (parseFloat(user.balance) < userCost) {
      throw new Error('Insufficient balance');
    }

    return true;
  }

  /**
   * 获取对话统计
   */
  async getConversationStats(userId, characterId = null) {
    const stats = {
      total_messages: 0,
      total_tokens: 0,
      total_cost: 0,
      avg_response_time: 0,
      favorite_characters: []
    };

    // TODO: 实现统计逻辑
    return stats;
  }
}

// 创建单例实例
const conversationService = new ConversationService();

module.exports = conversationService;
