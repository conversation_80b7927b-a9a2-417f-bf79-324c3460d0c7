const BaseModel = require('./BaseModel');

class AICharacter extends BaseModel {
  constructor() {
    super('ai_characters');
  }

  /**
   * 获取在线角色列表
   * @param {Object} options - 查询选项
   * @returns {Object} 分页结果
   */
  async getOnlineCharacters(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      orderBy = 'sort_order',
      orderDirection = 'ASC'
    } = options;

    return await this.paginate({
      page,
      pageSize,
      conditions: { status: 'online' },
      orderBy,
      orderDirection
    });
  }

  /**
   * 获取角色详情（包含系统提示词）
   * @param {Number} id - 角色ID
   * @returns {Object|null} 角色详情
   */
  async getCharacterDetail(id) {
    const character = await this.findById(id);
    if (!character || character.status !== 'online') {
      return null;
    }
    return character;
  }

  /**
   * 获取角色基本信息（不包含系统提示词）
   * @param {Number} id - 角色ID
   * @returns {Object|null} 角色基本信息
   */
  async getCharacterBasicInfo(id) {
    const sql = `
      SELECT id, name, avatar_url, description, popularity, status, sort_order, created_at, updated_at
      FROM ${this.tableName} 
      WHERE id = ? AND status = 'online'
    `;
    const rows = await this.query(sql, [id]);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 增加角色热度
   * @param {Number} id - 角色ID
   * @param {Number} increment - 增加数量，默认1
   * @returns {Boolean} 是否成功
   */
  async increasePopularity(id, increment = 1) {
    const sql = `UPDATE ${this.tableName} SET popularity = popularity + ? WHERE id = ?`;
    const result = await this.query(sql, [increment, id]);
    return result.affectedRows > 0;
  }

  /**
   * 获取热门角色
   * @param {Number} limit - 限制数量
   * @returns {Array} 角色列表
   */
  async getPopularCharacters(limit = 10) {
    const sql = `
      SELECT id, name, avatar_url, description, popularity, sort_order
      FROM ${this.tableName} 
      WHERE status = 'online'
      ORDER BY popularity DESC, sort_order ASC
      LIMIT ?
    `;
    return await this.query(sql, [limit]);
  }

  /**
   * 搜索角色
   * @param {String} keyword - 搜索关键词
   * @param {Object} options - 查询选项
   * @returns {Object} 分页结果
   */
  async searchCharacters(keyword, options = {}) {
    const {
      page = 1,
      pageSize = 10
    } = options;

    const offset = (page - 1) * pageSize;
    const searchPattern = `%${keyword}%`;

    const sql = `
      SELECT id, name, avatar_url, description, popularity, sort_order, created_at, updated_at
      FROM ${this.tableName} 
      WHERE status = 'online' AND (name LIKE ? OR description LIKE ?)
      ORDER BY popularity DESC, sort_order ASC
      LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
    `;

    const countSql = `
      SELECT COUNT(*) as count
      FROM ${this.tableName}
      WHERE status = 'online' AND (name LIKE ? OR description LIKE ?)
    `;

    const [list, countResult] = await Promise.all([
      this.query(sql, [searchPattern, searchPattern]),
      this.query(countSql, [searchPattern, searchPattern])
    ]);

    return {
      list,
      total: countResult[0].count,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };
  }

  /**
   * 检查角色是否可用
   * @param {Number} id - 角色ID
   * @returns {Boolean} 是否可用
   */
  async isCharacterAvailable(id) {
    const character = await this.findOneWhere({ id, status: 'online' });
    return !!character;
  }
}

module.exports = new AICharacter();
