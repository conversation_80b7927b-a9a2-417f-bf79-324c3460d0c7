{"name": "tree-hole-backend", "version": "1.0.0", "description": "树洞聊天系统后端API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "node test/run-tests.js", "test:auth": "node test/run-tests.js auth.test.js", "test:public": "node test/run-tests.js public.test.js", "test:chat": "node test/run-tests.js chat.test.js", "test:user": "node test/run-tests.js user.test.js", "test:payment": "node test/run-tests.js payment.test.js", "test:misc": "node test/run-tests.js misc.test.js", "test:coverage": "jest --config test/jest.config.js --coverage", "test:watch": "jest --config test/jest.config.js --watch"}, "keywords": ["chat", "ai", "api", "node.js"], "author": "YZFly", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "openai": "^5.10.1", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}