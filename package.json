{"name": "tree-hole-backend", "version": "1.0.0", "description": "树洞聊天系统后端API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest"}, "keywords": ["chat", "ai", "api", "node.js"], "author": "YZFly", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}