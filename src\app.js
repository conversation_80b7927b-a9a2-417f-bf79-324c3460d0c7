const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// 导入配置和中间件
const { initDatabase } = require('./config/database');
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');
const routes = require('./routes');

// 创建Express应用
const app = express();

// 基础中间件
app.use(helmet()); // 安全头
app.use(cors()); // 跨域支持
app.use(express.json({ limit: '10mb' })); // JSON解析
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // URL编码解析

// 请求日志中间件（简单版本）
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path} - ${req.ip}`);
  next();
});

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000次请求
  message: {
    code: 42901,
    message: '请求过于频繁，请稍后再试',
    data: null
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use(limiter);

// 特殊接口的限流
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 认证相关接口限制更严格
  message: {
    code: 42902,
    message: '认证请求过于频繁，请稍后再试',
    data: null
  }
});

// 对认证接口应用更严格的限流
app.use('/api/v1/auth/login', authLimiter);
app.use('/api/v1/auth/register', authLimiter);
app.use('/api/v1/auth/send-code', authLimiter);

// 注册路由
app.use('/', routes);

// 404处理
app.use(notFoundHandler);

// 全局错误处理
app.use(errorHandler);

// 启动服务器
const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    // 初始化数据库
    console.log('正在初始化数据库...');
    await initDatabase();
    console.log('数据库初始化完成');

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    树洞聊天系统后端 API                        ║
║                                                              ║
║  🚀 服务器启动成功                                             ║
║  📡 端口: ${PORT}                                           ║
║  🌍 环境: ${process.env.NODE_ENV || 'development'}                                        ║
║  📖 API文档: http://localhost:${PORT}/api/v1/docs              ║
║  ❤️  健康检查: http://localhost:${PORT}/health                 ║
║                                                              ║
║  主要接口:                                                    ║
║  • POST /api/v1/auth/register     - 用户注册                  ║
║  • POST /api/v1/auth/login        - 用户登录                  ║
║  • GET  /api/v1/ai-characters     - 获取AI角色列表             ║
║  • POST /api/v1/chat/messages     - 发送消息                  ║
║  • GET  /api/v1/user/profile      - 获取用户信息               ║
║  • POST /api/v1/payment/orders    - 创建充值订单               ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
      `);
    });

  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭服务器...');
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
startServer();

module.exports = app;
