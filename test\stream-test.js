const http = require('http');

console.log('🌊 DeepSeek V3 Fast 流式响应测试...\n');

// HTTP请求工具
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(body);
          resolve({ status: res.statusCode, data: json });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      console.log('请求错误:', err.message);
      resolve({ status: 0, data: null });
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 流式请求工具
function makeStreamRequest(path, data, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      console.log('📡 流式响应状态码:', res.statusCode);
      console.log('📡 响应头:', res.headers);

      if (res.statusCode !== 200) {
        let errorBody = '';
        res.on('data', chunk => errorBody += chunk);
        res.on('end', () => {
          console.log('❌ 流式请求失败:', errorBody);
          resolve({ success: false, error: errorBody });
        });
        return;
      }

      const chunks = [];
      let content = '';

      res.on('data', (chunk) => {
        const chunkStr = chunk.toString();
        chunks.push(chunkStr);
        
        // 解析SSE数据
        const lines = chunkStr.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.substring(6);
            if (dataStr === '[DONE]') {
              console.log('✅ 流式响应完成');
              resolve({ success: true, chunks, content });
              return;
            }
            
            try {
              const data = JSON.parse(dataStr);
              if (data.type === 'content') {
                content += data.content;
                process.stdout.write(data.content);
              } else if (data.type === 'done') {
                console.log('\n📊 最终统计:');
                console.log('   完整内容:', data.content);
                console.log('   Token使用:', data.token_usage);
                console.log('   费用:', data.cost);
                console.log('   响应时间:', data.response_time + 'ms');
                resolve({ success: true, chunks, content: data.content, stats: data });
                return;
              } else if (data.type === 'error') {
                console.log('\n❌ 流式响应错误:', data.error);
                resolve({ success: false, error: data.error });
                return;
              }
            } catch (e) {
              // 忽略解析错误，可能是不完整的数据
            }
          }
        }
      });

      res.on('end', () => {
        console.log('\n✅ 流式响应结束');
        resolve({ success: true, chunks, content });
      });

      res.on('error', (err) => {
        console.log('\n❌ 流式响应错误:', err.message);
        resolve({ success: false, error: err.message });
      });
    });

    req.on('error', (err) => {
      console.log('❌ 请求错误:', err.message);
      resolve({ success: false, error: err.message });
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

async function testStreamResponse() {
  console.log('📋 测试配置:');
  console.log('   模型: DeepSeek-V3-Fast');
  console.log('   流式响应: true');
  console.log('   测试消息: "请用一句话介绍你自己"\n');

  // 1. 注册用户获取token
  console.log('🔐 注册测试用户...');
  const registerResult = await makeRequest('POST', '/api/v1/auth/register', {
    email: `stream_test_${Date.now()}@example.com`,
    password: 'password123'
  });

  if (registerResult.status !== 200 || registerResult.data.code !== 0) {
    console.log('❌ 用户注册失败:', registerResult.data);
    return;
  }

  const token = registerResult.data.data.access_token;
  console.log('✅ 用户注册成功，获得token\n');

  // 2. 测试普通响应（对比）
  console.log('📋 测试普通响应（对比）...');
  const normalResult = await makeRequest('POST', '/api/v1/chat/messages', {
    character_id: 1,
    content: '请用一句话介绍你自己',
    stream: false,
    model: 'DeepSeek-V3-Fast'
  }, {
    'Authorization': `Bearer ${token}`
  });

  if (normalResult.status === 200 && normalResult.data.code === 0) {
    console.log('✅ 普通响应成功');
    console.log('   AI回复:', normalResult.data.data.reply_message.content);
    console.log('   响应时间:', normalResult.data.data.response_time + 'ms\n');
  } else {
    console.log('❌ 普通响应失败:', normalResult.data);
    return;
  }

  // 3. 测试流式响应
  console.log('🌊 测试流式响应...');
  console.log('💬 AI回复: ');
  
  const streamResult = await makeStreamRequest('/api/v1/chat/messages', {
    character_id: 1,
    content: '请详细介绍一下你自己，包括你的能力和特点',
    stream: true,
    model: 'DeepSeek-V3-Fast'
  }, {
    'Authorization': `Bearer ${token}`
  });

  console.log('\n' + '='.repeat(60));
  console.log('🌊 流式响应测试结果');
  console.log('='.repeat(60));

  if (streamResult.success) {
    console.log('✅ 流式响应测试成功！');
    console.log('📊 统计信息:');
    if (streamResult.stats) {
      console.log('   Token使用:', streamResult.stats.token_usage);
      console.log('   费用:', streamResult.stats.cost);
      console.log('   响应时间:', streamResult.stats.response_time + 'ms');
    }
    console.log('   数据块数量:', streamResult.chunks.length);
    console.log('   完整内容长度:', streamResult.content.length);
    
    console.log('\n🎉 DeepSeek V3 Fast 流式响应集成成功！');
    console.log('🚀 特性:');
    console.log('• ⚡ 实时流式输出，用户体验更佳');
    console.log('• 📊 精确Token统计和费用计算');
    console.log('• 🔄 支持长文本生成');
    console.log('• 💳 实时余额扣除');
  } else {
    console.log('❌ 流式响应测试失败');
    console.log('错误:', streamResult.error);
    
    console.log('\n🔧 可能的问题:');
    console.log('• 服务器配置问题');
    console.log('• OpenAI API连接问题');
    console.log('• 流式响应处理逻辑问题');
  }
}

testStreamResponse().catch(console.error);
