const BaseModel = require('./BaseModel');

class UserMemory extends BaseModel {
  constructor() {
    super('user_memories');
  }

  /**
   * 创建用户记忆
   * @param {Object} memoryData - 记忆数据
   * @returns {Object} 创建的记忆对象
   */
  async createMemory(memoryData) {
    const {
      user_id,
      character_id = null,
      memory_type,
      memory_content,
      memory_summary = null,
      importance_score = 0.5,
      emotional_tone = 'neutral',
      tags = null,
      related_message_id = null,
      expires_at = null
    } = memoryData;

    const sql = `
      INSERT INTO ${this.tableName}
      (user_id, character_id, memory_type, memory_content, memory_summary, 
       importance_score, emotional_tone, tags, related_message_id, expires_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const tagsJson = tags ? JSON.stringify(tags) : null;
    const result = await this.query(sql, [
      user_id, character_id, memory_type, memory_content, memory_summary,
      importance_score, emotional_tone, tagsJson, related_message_id, expires_at
    ]);

    return await this.findById(result.insertId);
  }

  /**
   * 获取用户记忆
   * @param {Number} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Array} 记忆列表
   */
  async getUserMemories(userId, options = {}) {
    const {
      character_id = null,
      memory_type = null,
      importance_threshold = 0,
      limit = 50,
      offset = 0,
      include_expired = false
    } = options;

    let sql = `
      SELECT um.*, ac.name as character_name
      FROM ${this.tableName} um
      LEFT JOIN ai_characters ac ON um.character_id = ac.id
      WHERE um.user_id = ?
    `;
    
    const params = [userId];

    if (character_id !== null && character_id !== undefined) {
      sql += ` AND um.character_id = ?`;
      params.push(character_id);
    }

    if (memory_type) {
      sql += ` AND um.memory_type = ?`;
      params.push(memory_type);
    }

    if (importance_threshold > 0) {
      sql += ` AND um.importance_score >= ?`;
      params.push(importance_threshold);
    }

    if (!include_expired) {
      sql += ` AND (um.expires_at IS NULL OR um.expires_at > NOW())`;
    }

    sql += ` ORDER BY um.importance_score DESC, um.created_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    const results = await this.query(sql, params);
    
    return results.map(memory => ({
      ...memory,
      tags: memory.tags ? JSON.parse(memory.tags) : null
    }));
  }

  /**
   * 搜索记忆
   * @param {Number} userId - 用户ID
   * @param {String} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Array} 匹配的记忆
   */
  async searchMemories(userId, keyword, options = {}) {
    const {
      character_id = null,
      memory_type = null,
      emotional_tone = null,
      limit = 20
    } = options;

    let sql = `
      SELECT um.*, ac.name as character_name
      FROM ${this.tableName} um
      LEFT JOIN ai_characters ac ON um.character_id = ac.id
      WHERE um.user_id = ?
      AND (um.memory_content LIKE ? OR um.memory_summary LIKE ? OR JSON_SEARCH(um.tags, 'one', ?) IS NOT NULL)
      AND (um.expires_at IS NULL OR um.expires_at > NOW())
    `;
    
    const searchPattern = `%${keyword}%`;
    const params = [userId, searchPattern, searchPattern, keyword];

    if (character_id !== null && character_id !== undefined) {
      sql += ` AND um.character_id = ?`;
      params.push(character_id);
    }

    if (memory_type) {
      sql += ` AND um.memory_type = ?`;
      params.push(memory_type);
    }

    if (emotional_tone) {
      sql += ` AND um.emotional_tone = ?`;
      params.push(emotional_tone);
    }

    sql += ` ORDER BY um.importance_score DESC, um.created_at DESC LIMIT ${parseInt(limit)}`;

    const results = await this.query(sql, params);
    
    return results.map(memory => ({
      ...memory,
      tags: memory.tags ? JSON.parse(memory.tags) : null
    }));
  }

  /**
   * 更新记忆重要性
   * @param {Number} memoryId - 记忆ID
   * @param {Number} importanceScore - 重要性评分
   * @returns {Boolean} 是否更新成功
   */
  async updateImportance(memoryId, importanceScore) {
    const sql = `
      UPDATE ${this.tableName}
      SET importance_score = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const result = await this.query(sql, [importanceScore, memoryId]);
    return result.affectedRows > 0;
  }

  /**
   * 添加记忆标签
   * @param {Number} memoryId - 记忆ID
   * @param {Array} newTags - 新标签
   * @returns {Boolean} 是否更新成功
   */
  async addTags(memoryId, newTags) {
    const memory = await this.findById(memoryId);
    if (!memory) return false;

    const existingTags = memory.tags ? JSON.parse(memory.tags) : [];
    const updatedTags = [...new Set([...existingTags, ...newTags])];

    const sql = `
      UPDATE ${this.tableName}
      SET tags = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const result = await this.query(sql, [JSON.stringify(updatedTags), memoryId]);
    return result.affectedRows > 0;
  }

  /**
   * 获取记忆统计
   * @param {Number} userId - 用户ID
   * @param {Number} characterId - 角色ID (可选)
   * @returns {Object} 统计信息
   */
  async getMemoryStats(userId, characterId = null) {
    let sql = `
      SELECT 
        memory_type,
        emotional_tone,
        COUNT(*) as count,
        AVG(importance_score) as avg_importance,
        MAX(created_at) as latest_memory
      FROM ${this.tableName}
      WHERE user_id = ?
      AND (expires_at IS NULL OR expires_at > NOW())
    `;
    
    const params = [userId];

    if (characterId !== null && characterId !== undefined) {
      sql += ` AND character_id = ?`;
      params.push(characterId);
    }

    sql += ` GROUP BY memory_type, emotional_tone`;

    const results = await this.query(sql, params);

    // 总计统计
    let totalSql = `
      SELECT
        COUNT(*) as total_memories,
        AVG(importance_score) as avg_importance,
        COUNT(DISTINCT character_id) as characters_count
      FROM ${this.tableName}
      WHERE user_id = ?
      AND (expires_at IS NULL OR expires_at > NOW())
    `;

    const totalParams = [userId];
    if (characterId !== null && characterId !== undefined) {
      totalSql += ` AND character_id = ?`;
      totalParams.push(characterId);
    }
    
    const totalResult = await this.query(totalSql, totalParams);
    
    return {
      total: totalResult[0],
      breakdown: results
    };
  }

  /**
   * 清理过期记忆
   * @param {Number} userId - 用户ID (可选)
   * @returns {Number} 清理数量
   */
  async cleanupExpiredMemories(userId = null) {
    let sql = `
      DELETE FROM ${this.tableName}
      WHERE expires_at IS NOT NULL AND expires_at <= NOW()
    `;
    
    const params = [];
    
    if (userId) {
      sql += ` AND user_id = ?`;
      params.push(userId);
    }
    
    const result = await this.query(sql, params);
    return result.affectedRows;
  }

  /**
   * 获取相关记忆
   * @param {Number} userId - 用户ID
   * @param {Array} keywords - 关键词列表
   * @param {Number} limit - 限制数量
   * @returns {Array} 相关记忆
   */
  async getRelatedMemories(userId, keywords, limit = 10) {
    if (!keywords || keywords.length === 0) return [];

    const keywordConditions = keywords.map(() => 
      `(um.memory_content LIKE ? OR um.memory_summary LIKE ? OR JSON_SEARCH(um.tags, 'one', ?) IS NOT NULL)`
    ).join(' OR ');

    const sql = `
      SELECT um.*, ac.name as character_name,
        (${keywords.map(() => 
          `(CASE WHEN um.memory_content LIKE ? THEN 1 ELSE 0 END + 
            CASE WHEN um.memory_summary LIKE ? THEN 1 ELSE 0 END + 
            CASE WHEN JSON_SEARCH(um.tags, 'one', ?) IS NOT NULL THEN 1 ELSE 0 END)`
        ).join(' + ')}) as relevance_score
      FROM ${this.tableName} um
      LEFT JOIN ai_characters ac ON um.character_id = ac.id
      WHERE um.user_id = ?
      AND (um.expires_at IS NULL OR um.expires_at > NOW())
      AND (${keywordConditions})
      ORDER BY relevance_score DESC, um.importance_score DESC
      LIMIT ${parseInt(limit)}
    `;

    const params = [];
    
    // 添加关键词参数 (每个关键词需要3次，用于relevance_score计算)
    keywords.forEach(keyword => {
      const pattern = `%${keyword}%`;
      params.push(pattern, pattern, keyword);
    });
    
    // 添加关键词参数 (用于WHERE条件)
    keywords.forEach(keyword => {
      const pattern = `%${keyword}%`;
      params.push(pattern, pattern, keyword);
    });
    
    params.push(userId);

    const results = await this.query(sql, params);
    
    return results.map(memory => ({
      ...memory,
      tags: memory.tags ? JSON.parse(memory.tags) : null
    }));
  }
}

module.exports = new UserMemory();
