const { error, ERROR_CODES } = require('../utils/response');

/**
 * 全局错误处理中间件
 */
function errorHandler(err, req, res, next) {
  console.error('Error:', err);

  // 如果响应已经发送，则交给默认错误处理器
  if (res.headersSent) {
    return next(err);
  }

  // Joi验证错误
  if (err.isJoi) {
    return error(res, ERROR_CODES.BAD_REQUEST, err.details[0].message);
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    return error(res, ERROR_CODES.UNAUTHORIZED, 'Token无效');
  }

  if (err.name === 'TokenExpiredError') {
    return error(res, ERROR_CODES.UNAUTHORIZED, 'Token已过期');
  }

  // 数据库错误
  if (err.code === 'ER_DUP_ENTRY') {
    return error(res, ERROR_CODES.CONFLICT, '数据已存在');
  }

  // 自定义业务错误
  if (err.code && err.message) {
    return error(res, err.code, err.message);
  }

  // 默认服务器错误
  return error(res, ERROR_CODES.INTERNAL_ERROR, '服务器内部错误');
}

/**
 * 404处理中间件
 */
function notFoundHandler(req, res, next) {
  error(res, ERROR_CODES.NOT_FOUND, `接口 ${req.method} ${req.path} 不存在`);
}

module.exports = {
  errorHandler,
  notFoundHandler
};
