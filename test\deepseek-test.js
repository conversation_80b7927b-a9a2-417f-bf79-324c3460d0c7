const http = require('http');

console.log('🤖 开始DeepSeek V3 Fast测试...\n');

// HTTP请求工具
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(body);
          resolve({ status: res.statusCode, data: json });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', () => resolve({ status: 0, data: null }));

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runDeepSeekTests() {
  let passed = 0;
  let total = 0;
  let token = null;

  console.log('🔧 配置信息:');
  console.log('   模型: DeepSeek V3 Fast');
  console.log('   API地址: https://www.sophnet.com/api/open-apis/v1');
  console.log('   定价: ¥2/M输入 + ¥8/M输出 (限时五折)\n');

  // 测试1: 用户注册获取token
  console.log('📋 测试用户注册...');
  total++;
  const registerResult = await makeRequest('POST', '/api/v1/auth/register', {
    email: `deepseek${Date.now()}@example.com`,
    password: 'password123'
  });

  if (registerResult.status === 200 && registerResult.data.code === 0) {
    console.log('✅ 用户注册成功');
    token = registerResult.data.data.access_token;
    passed++;
  } else {
    console.log('❌ 用户注册失败');
    console.log('响应:', registerResult.data);
  }

  if (!token) {
    console.log('❌ 无法获取认证token，跳过后续测试');
    return;
  }

  // 测试2: AI服务健康检查
  console.log('📋 测试AI服务健康检查...');
  total++;
  const healthResult = await makeRequest('GET', '/api/v1/ai/health');

  if (healthResult.status === 200 && healthResult.data.code === 0) {
    console.log('✅ AI服务健康检查通过');
    console.log('   默认模型:', healthResult.data.data.config.default_model);
    passed++;
  } else {
    console.log('❌ AI服务健康检查失败');
    console.log('响应:', healthResult.data);
  }

  // 测试3: 获取模型定价信息
  console.log('📋 测试获取模型定价信息...');
  total++;
  const pricingResult = await makeRequest('GET', '/api/v1/ai/pricing', null, {
    'Authorization': `Bearer ${token}`
  });

  if (pricingResult.status === 200 && pricingResult.data.code === 0) {
    console.log('✅ 获取定价信息成功');
    const deepseekPricing = pricingResult.data.data.pricing['DeepSeek-V3-Fast'];
    if (deepseekPricing) {
      console.log('   DeepSeek V3 Fast定价:');
      console.log(`     输入: ¥${deepseekPricing.input_price_per_1k}/1K tokens`);
      console.log(`     输出: ¥${deepseekPricing.output_price_per_1k}/1K tokens`);
    }
    passed++;
  } else {
    console.log('❌ 获取定价信息失败');
    console.log('响应:', pricingResult.data);
  }

  // 测试4: DeepSeek对话测试
  console.log('📋 测试DeepSeek V3 Fast对话...');
  total++;
  const testChatResult = await makeRequest('POST', '/api/v1/ai/test', {
    message: '你好，请简单介绍一下你自己',
    model: 'DeepSeek-V3-Fast'
  }, {
    'Authorization': `Bearer ${token}`
  });

  if (testChatResult.status === 200 && testChatResult.data.code === 0) {
    console.log('✅ DeepSeek对话测试成功');
    console.log('   AI回复:', testChatResult.data.data.test_response.content.substring(0, 100) + '...');
    console.log('   使用模型:', testChatResult.data.data.model_used);
    console.log('   响应时间:', testChatResult.data.data.test_response.responseTime + 'ms');
    passed++;
  } else {
    console.log('❌ DeepSeek对话测试失败');
    console.log('响应:', testChatResult.data);
  }

  // 测试5: 真实聊天接口测试
  console.log('📋 测试真实聊天接口...');
  total++;
  const chatResult = await makeRequest('POST', '/api/v1/chat/messages', {
    character_id: 1,
    content: '你好，我想和你聊聊天，请简单回复',
    stream: false,
    model: 'DeepSeek-V3-Fast'
  }, {
    'Authorization': `Bearer ${token}`
  });

  if (chatResult.status === 200 && chatResult.data.code === 0) {
    console.log('✅ 聊天接口测试成功');
    console.log('   AI回复:', chatResult.data.data.reply_message.content.substring(0, 100) + '...');
    console.log('   Token使用:', chatResult.data.data.token_usage);
    console.log('   费用:', chatResult.data.data.cost);
    console.log('   余额变化:', chatResult.data.data.balance_change);
    passed++;
  } else {
    console.log('❌ 聊天接口测试失败');
    console.log('响应:', chatResult.data);
  }

  // 输出结果
  console.log('\n' + '='.repeat(60));
  console.log('🤖 DeepSeek V3 Fast测试结果');
  console.log('='.repeat(60));
  console.log(`✅ 通过: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 所有DeepSeek V3 Fast测试都通过了！');
    console.log('🚀 系统已成功集成DeepSeek V3 Fast模型！');
    console.log('\n📋 DeepSeek V3 Fast特性:');
    console.log('• 🧠 671B参数，37B激活参数');
    console.log('• ⚡ 高TPS极速版，响应更快');
    console.log('• 💰 限时五折优惠 (¥2/M输入 + ¥8/M输出)');
    console.log('• 📝 32K上下文长度');
    console.log('• 🔧 代码与数学能力更强');
    console.log('\n🔄 可用功能:');
    console.log('• 普通对话 (stream: false)');
    console.log('• 流式对话 (stream: true)');
    console.log('• 多轮对话上下文');
    console.log('• 精确Token计费');
    console.log('• 实时余额扣除');
  } else {
    console.log(`\n💥 ${total - passed} 个测试失败，需要检查配置。`);
    console.log('\n🔧 检查项目:');
    console.log('• API密钥是否正确');
    console.log('• 网络连接是否正常');
    console.log('• 模型名称是否匹配');
  }
}

runDeepSeekTests().catch(console.error);
