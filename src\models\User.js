const BaseModel = require('./BaseModel');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

class User extends BaseModel {
  constructor() {
    super('users');
  }

  /**
   * 根据手机号或邮箱查找用户
   * @param {String} identity - 手机号或邮箱
   * @returns {Object|null} 用户对象或null
   */
  async findByIdentity(identity) {
    const sql = `SELECT * FROM ${this.tableName} WHERE phone_number = ? OR email = ?`;
    const rows = await this.query(sql, [identity, identity]);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 根据UID查找用户
   * @param {String} uid - 用户UID
   * @returns {Object|null} 用户对象或null
   */
  async findByUid(uid) {
    return await this.findOneWhere({ uid });
  }

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @returns {Object} 创建的用户
   */
  async createUser(userData) {
    const { phone_number, email, password } = userData;
    
    // 生成UID
    const uid = 'u-' + uuidv4().replace(/-/g, '').substring(0, 8);
    
    // 加密密码
    const password_hash = await bcrypt.hash(password, 10);
    
    // 创建用户数据
    const data = {
      uid,
      phone_number: phone_number || null,
      email: email || null,
      password_hash,
      balance: '10.0000', // 新用户赠送10余额
      role: 'user',
      status: 'active'
    };

    return await this.create(data);
  }

  /**
   * 验证密码
   * @param {String} password - 明文密码
   * @param {String} hash - 密码哈希
   * @returns {Boolean} 是否匹配
   */
  async verifyPassword(password, hash) {
    return await bcrypt.compare(password, hash);
  }

  /**
   * 更新密码
   * @param {Number} userId - 用户ID
   * @param {String} newPassword - 新密码
   * @returns {Boolean} 是否更新成功
   */
  async updatePassword(userId, newPassword) {
    const password_hash = await bcrypt.hash(newPassword, 10);
    const result = await this.update(userId, { password_hash });
    return !!result;
  }

  /**
   * 更新用户余额
   * @param {Number} userId - 用户ID
   * @param {String} amount - 变动金额（可为负数）
   * @returns {Object} 更新后的用户信息
   */
  async updateBalance(userId, amount) {
    const sql = `UPDATE ${this.tableName} SET balance = balance + ? WHERE id = ?`;
    await this.query(sql, [amount, userId]);
    return await this.findById(userId);
  }

  /**
   * 获取用户公开信息（隐藏敏感信息）
   * @param {Object} user - 用户对象
   * @returns {Object} 公开信息
   */
  getPublicInfo(user) {
    if (!user) return null;
    
    const { password_hash, ...publicInfo } = user;
    
    // 脱敏处理
    if (publicInfo.phone_number) {
      publicInfo.phone_number = publicInfo.phone_number.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    
    if (publicInfo.email) {
      const [username, domain] = publicInfo.email.split('@');
      if (username.length > 2) {
        publicInfo.email = username.substring(0, 2) + '***@' + domain;
      }
    }
    
    return publicInfo;
  }

  /**
   * 检查用户是否存在
   * @param {String} phone_number - 手机号
   * @param {String} email - 邮箱
   * @returns {Boolean} 是否存在
   */
  async checkUserExists(phone_number, email) {
    let sql = `SELECT id FROM ${this.tableName} WHERE `;
    const conditions = [];
    const values = [];

    if (phone_number) {
      conditions.push('phone_number = ?');
      values.push(phone_number);
    }

    if (email) {
      conditions.push('email = ?');
      values.push(email);
    }

    if (conditions.length === 0) return false;

    sql += conditions.join(' OR ');
    const rows = await this.query(sql, values);
    return rows.length > 0;
  }
}

module.exports = new User();
