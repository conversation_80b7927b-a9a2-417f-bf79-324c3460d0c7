const express = require('express');
const router = express.Router();
const Joi = require('joi');
const PublicController = require('../controllers/PublicController');
const { validate, schemas } = require('../middleware/validation');

// 获取AI角色列表
router.get('/ai-characters',
  validate(schemas.pagination, 'query'),
  PublicController.getAICharacters
);

// 获取单个AI角色详情
router.get('/ai-characters/:id',
  validate(schemas.idParam, 'params'),
  PublicController.getAICharacterDetail
);

// 获取热门AI角色
router.get('/ai-characters/popular',
  validate(Joi.object({
    limit: Joi.number().integer().min(1).max(50).optional().default(10)
  }), 'query'),
  PublicController.getPopularCharacters
);

// 搜索AI角色
router.get('/ai-characters/search',
  validate(Joi.object({
    keyword: Joi.string().min(1).max(50).required(),
    page: Joi.number().integer().min(1).optional().default(1),
    pageSize: Joi.number().integer().min(1).max(50).optional().default(10)
  }), 'query'),
  PublicController.searchCharacters
);

// 获取充值套餐列表
router.get('/recharge-packages',
  PublicController.getRechargePackages
);

// 获取推荐充值套餐
router.get('/recharge-packages/recommended',
  validate(Joi.object({
    limit: Joi.number().integer().min(1).max(10).optional().default(3)
  }), 'query'),
  PublicController.getRecommendedPackages
);

// 获取套餐按性价比排序
router.get('/recharge-packages/by-value',
  PublicController.getPackagesByValue
);

// 根据价格范围获取套餐
router.get('/recharge-packages/by-price',
  validate(Joi.object({
    minPrice: Joi.number().min(0).required(),
    maxPrice: Joi.number().min(0).required()
  }), 'query'),
  PublicController.getPackagesByPriceRange
);

// 获取网站统计信息
router.get('/stats',
  PublicController.getSiteStats
);

// 获取系统公告
router.get('/announcements',
  PublicController.getAnnouncements
);

// 获取帮助文档
router.get('/help',
  PublicController.getHelpDocs
);

module.exports = router;
