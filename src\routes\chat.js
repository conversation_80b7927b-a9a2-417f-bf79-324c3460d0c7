const express = require('express');
const router = express.Router();
const Joi = require('joi');
const ChatController = require('../controllers/ChatController');
const { authenticate } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// 所有聊天接口都需要认证
router.use(authenticate);

// 发送消息并获取AI回复
router.post('/messages',
  validate(schemas.sendMessage),
  (req, res, next) => ChatController.sendMessage(req, res, next)
);

// 获取历史聊天记录
router.get('/sessions/:character_id/messages',
  validate(schemas.idParam, 'params'),
  validate(schemas.getMessages, 'query'),
  (req, res, next) => ChatController.getSessionMessages(req, res, next)
);

// 获取会话列表
router.get('/sessions',
  validate(schemas.pagination, 'query'),
  (req, res, next) => ChatController.getSessions(req, res, next)
);

// 删除会话
router.delete('/sessions/:session_id',
  validate(Joi.object({
    session_id: Joi.number().integer().positive().required()
  }), 'params'),
  (req, res, next) => ChatController.deleteSession(req, res, next)
);

// 获取会话统计
router.get('/sessions/stats',
  (req, res, next) => ChatController.getSessionStats(req, res, next)
);

// 搜索消息内容
router.get('/sessions/:character_id/messages/search',
  validate(schemas.idParam, 'params'),
  validate(Joi.object({
    keyword: Joi.string().min(1).max(100).required(),
    limit: Joi.number().integer().min(1).max(100).optional().default(50)
  }), 'query'),
  (req, res, next) => ChatController.searchMessages(req, res, next)
);

module.exports = router;
