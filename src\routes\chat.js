const express = require('express');
const router = express.Router();
const Joi = require('joi');
const ChatController = require('../controllers/ChatController');
const { authenticate } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// 所有聊天接口都需要认证
router.use(authenticate);

// 发送消息并获取AI回复
router.post('/messages',
  validate(schemas.sendMessage),
  ChatController.sendMessage
);

// 获取历史聊天记录
router.get('/sessions/:character_id/messages',
  validate(schemas.idParam, 'params'),
  validate(schemas.getMessages, 'query'),
  ChatController.getSessionMessages
);

// 获取会话列表
router.get('/sessions',
  validate(schemas.pagination, 'query'),
  ChatController.getSessions
);

// 删除会话
router.delete('/sessions/:session_id',
  validate(Joi.object({
    session_id: Joi.number().integer().positive().required()
  }), 'params'),
  ChatController.deleteSession
);

// 获取会话统计
router.get('/sessions/stats',
  ChatController.getSessionStats
);

// 搜索消息内容
router.get('/sessions/:character_id/messages/search',
  validate(schemas.idParam, 'params'),
  validate(Joi.object({
    keyword: Joi.string().min(1).max(100).required(),
    limit: Joi.number().integer().min(1).max(100).optional().default(50)
  }), 'query'),
  ChatController.searchMessages
);

module.exports = router;
