/**
 * 统一响应格式工具类
 */

// 错误码定义
const ERROR_CODES = {
  SUCCESS: 0,
  BAD_REQUEST: 40001,
  UNAUTHORIZED: 40101,
  INVALID_CREDENTIALS: 40102,
  FORBIDDEN: 40301,
  NOT_FOUND: 40401,
  CONFLICT: 40901,
  INSUFFICIENT_BALANCE: 42201,
  INTERNAL_ERROR: 50001,
  AI_SERVICE_ERROR: 50002
};

// 错误消息定义
const ERROR_MESSAGES = {
  [ERROR_CODES.SUCCESS]: 'Success',
  [ERROR_CODES.BAD_REQUEST]: '请求参数校验失败',
  [ERROR_CODES.UNAUTHORIZED]: 'Token缺失或无效',
  [ERROR_CODES.INVALID_CREDENTIALS]: '用户名或密码错误',
  [ERROR_CODES.FORBIDDEN]: '权限不足，禁止访问',
  [ERROR_CODES.NOT_FOUND]: '请求的资源不存在',
  [ERROR_CODES.CONFLICT]: '资源冲突',
  [ERROR_CODES.INSUFFICIENT_BALANCE]: '余额不足',
  [ERROR_CODES.INTERNAL_ERROR]: '服务器内部未知错误',
  [ERROR_CODES.AI_SERVICE_ERROR]: '调用第三方AI服务失败'
};

// HTTP状态码映射
const HTTP_STATUS_MAP = {
  [ERROR_CODES.SUCCESS]: 200,
  [ERROR_CODES.BAD_REQUEST]: 400,
  [ERROR_CODES.UNAUTHORIZED]: 401,
  [ERROR_CODES.INVALID_CREDENTIALS]: 401,
  [ERROR_CODES.FORBIDDEN]: 403,
  [ERROR_CODES.NOT_FOUND]: 404,
  [ERROR_CODES.CONFLICT]: 409,
  [ERROR_CODES.INSUFFICIENT_BALANCE]: 422,
  [ERROR_CODES.INTERNAL_ERROR]: 500,
  [ERROR_CODES.AI_SERVICE_ERROR]: 500
};

/**
 * 成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 响应数据
 * @param {String} message - 响应消息
 */
function success(res, data = null, message = 'Success') {
  res.status(200).json({
    code: ERROR_CODES.SUCCESS,
    message,
    data
  });
}

/**
 * 错误响应
 * @param {Object} res - Express响应对象
 * @param {Number} code - 错误码
 * @param {String} message - 错误消息
 */
function error(res, code = ERROR_CODES.INTERNAL_ERROR, message = null) {
  const errorMessage = message || ERROR_MESSAGES[code] || '未知错误';
  const httpStatus = HTTP_STATUS_MAP[code] || 500;
  
  res.status(httpStatus).json({
    code,
    message: errorMessage,
    data: null
  });
}

/**
 * 分页响应
 * @param {Object} res - Express响应对象
 * @param {Array} list - 数据列表
 * @param {Number} total - 总数
 * @param {Number} page - 当前页
 * @param {Number} pageSize - 每页大小
 */
function paginated(res, list, total, page = 1, pageSize = 10) {
  success(res, {
    list,
    total,
    page: parseInt(page),
    pageSize: parseInt(pageSize),
    totalPages: Math.ceil(total / pageSize)
  });
}

module.exports = {
  ERROR_CODES,
  ERROR_MESSAGES,
  HTTP_STATUS_MAP,
  success,
  error,
  paginated
};
