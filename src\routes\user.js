const express = require('express');
const router = express.Router();
const Joi = require('joi');
const UserController = require('../controllers/UserController');
const { authenticate } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// 所有用户接口都需要认证
router.use(authenticate);

// 获取当前用户信息
router.get('/profile',
  UserController.getProfile
);

// 修改密码
router.put('/password',
  validate(schemas.changePassword),
  UserController.changePassword
);

// 获取消费记录
router.get('/consumption-logs',
  validate(Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    pageSize: Joi.number().integer().min(1).max(100).optional().default(10),
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().optional()
  }), 'query'),
  UserController.getConsumptionLogs
);

// 获取充值订单记录
router.get('/recharge-orders',
  validate(Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    pageSize: Joi.number().integer().min(1).max(100).optional().default(10),
    status: Joi.string().valid('pending', 'completed', 'failed', 'cancelled').optional()
  }), 'query'),
  UserController.getRechargeOrders
);

// 获取用户统计信息
router.get('/stats',
  UserController.getUserStats
);

// 获取消费统计（按时间段）
router.get('/consumption-stats',
  validate(Joi.object({
    period: Joi.string().valid('today', 'week', 'month', 'all').optional().default('month')
  }), 'query'),
  UserController.getConsumptionStats
);

// 获取每日消费趋势
router.get('/daily-consumption',
  validate(Joi.object({
    days: Joi.number().integer().min(1).max(365).optional().default(30)
  }), 'query'),
  UserController.getDailyConsumption
);

// 获取按角色的消费统计
router.get('/consumption-by-character',
  validate(Joi.object({
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().optional(),
    limit: Joi.number().integer().min(1).max(50).optional().default(10)
  }), 'query'),
  UserController.getConsumptionByCharacter
);

// 更新用户资料
router.put('/profile',
  validate(Joi.object({
    nickname: Joi.string().min(1).max(50).optional(),
    avatar_url: Joi.string().uri().optional()
  })),
  UserController.updateProfile
);

// 绑定手机号
router.post('/bind-phone',
  validate(Joi.object({
    phone_number: Joi.string().pattern(/^1[3-9]\d{9}$/).required(),
    verification_code: Joi.string().length(6).required()
  })),
  UserController.bindPhone
);

// 绑定邮箱
router.post('/bind-email',
  validate(Joi.object({
    email: Joi.string().email().required(),
    verification_code: Joi.string().length(6).required()
  })),
  UserController.bindEmail
);

// 注销账户
router.delete('/account',
  validate(Joi.object({
    password: Joi.string().required(),
    confirmation: Joi.string().valid('DELETE_MY_ACCOUNT').required()
  })),
  UserController.deleteAccount
);

module.exports = router;
