const { app, request, testUtils } = require('./setup');

describe('公开接口测试', () => {
  describe('GET /api/v1/ai-characters - 获取AI角色列表', () => {
    test('获取角色列表成功', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('list');
      expect(res.body.data).toHaveProperty('total');
      expect(res.body.data).toHaveProperty('page');
      expect(res.body.data).toHaveProperty('pageSize');
      expect(Array.isArray(res.body.data.list)).toBe(true);

      // 保存角色数据供其他测试使用
      if (res.body.data.list.length > 0) {
        global.testData.characters = res.body.data.list;
      }
    });

    test('分页参数正确', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters?page=1&pageSize=5')
        .expect(200);

      expect(res.body.data.page).toBe(1);
      expect(res.body.data.pageSize).toBe(5);
      expect(res.body.data.list.length).toBeLessThanOrEqual(5);
    });

    test('无效分页参数', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters?page=0&pageSize=101')
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/ai-characters/:id - 获取AI角色详情', () => {
    test('获取存在的角色详情', async () => {
      // 先获取角色列表
      const listRes = await request(app)
        .get('/api/v1/ai-characters')
        .expect(200);

      if (listRes.body.data.list.length > 0) {
        const characterId = listRes.body.data.list[0].id;
        
        const res = await request(app)
          .get(`/api/v1/ai-characters/${characterId}`)
          .expect(200);

        expect(res.body.code).toBe(0);
        expect(res.body.data).toHaveProperty('id');
        expect(res.body.data).toHaveProperty('name');
        expect(res.body.data).toHaveProperty('description');
        expect(res.body.data).toHaveProperty('avatar_url');
        expect(res.body.data).toHaveProperty('popularity');
        // 公开接口不应该返回system_prompt
        expect(res.body.data).not.toHaveProperty('system_prompt');
      }
    });

    test('获取不存在的角色', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters/99999')
        .expect(404);

      expect(res.body.code).toBe(40401);
      expect(res.body.message).toContain('不存在');
    });

    test('无效的角色ID', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters/invalid')
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/ai-characters/popular - 获取热门角色', () => {
    test('获取热门角色成功', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters/popular')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);
      expect(res.body.data.length).toBeLessThanOrEqual(10); // 默认限制10个
    });

    test('自定义限制数量', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters/popular?limit=3')
        .expect(200);

      expect(res.body.data.length).toBeLessThanOrEqual(3);
    });
  });

  describe('GET /api/v1/ai-characters/search - 搜索角色', () => {
    test('搜索角色成功', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters/search?keyword=温柔')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('list');
      expect(res.body.data).toHaveProperty('total');
    });

    test('空关键词搜索失败', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters/search?keyword=')
        .expect(400);

      expect(res.body.code).toBe(40001);
    });

    test('缺少关键词参数', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters/search')
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/recharge-packages - 获取充值套餐', () => {
    test('获取充值套餐成功', async () => {
      const res = await request(app)
        .get('/api/v1/recharge-packages')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);

      if (res.body.data.length > 0) {
        const package = res.body.data[0];
        expect(package).toHaveProperty('id');
        expect(package).toHaveProperty('name');
        expect(package).toHaveProperty('price');
        expect(package).toHaveProperty('balance_amount');
        expect(package).toHaveProperty('description');
        
        // 保存套餐数据供其他测试使用
        global.testData.packages = res.body.data;
      }
    });
  });

  describe('GET /api/v1/recharge-packages/recommended - 获取推荐套餐', () => {
    test('获取推荐套餐成功', async () => {
      const res = await request(app)
        .get('/api/v1/recharge-packages/recommended')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);
      expect(res.body.data.length).toBeLessThanOrEqual(3); // 默认限制3个
    });

    test('自定义推荐数量', async () => {
      const res = await request(app)
        .get('/api/v1/recharge-packages/recommended?limit=2')
        .expect(200);

      expect(res.body.data.length).toBeLessThanOrEqual(2);
    });
  });

  describe('GET /api/v1/recharge-packages/by-value - 按性价比获取套餐', () => {
    test('按性价比获取套餐成功', async () => {
      const res = await request(app)
        .get('/api/v1/recharge-packages/by-value')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);

      if (res.body.data.length > 1) {
        // 检查是否按性价比排序
        const first = res.body.data[0];
        const second = res.body.data[1];
        expect(first.value_ratio).toBeGreaterThanOrEqual(second.value_ratio);
      }
    });
  });

  describe('GET /api/v1/recharge-packages/by-price - 按价格范围获取套餐', () => {
    test('按价格范围获取套餐成功', async () => {
      const res = await request(app)
        .get('/api/v1/recharge-packages/by-price?minPrice=10&maxPrice=100')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);

      // 检查价格范围
      res.body.data.forEach(pkg => {
        expect(parseFloat(pkg.price)).toBeGreaterThanOrEqual(10);
        expect(parseFloat(pkg.price)).toBeLessThanOrEqual(100);
      });
    });

    test('缺少价格参数', async () => {
      const res = await request(app)
        .get('/api/v1/recharge-packages/by-price?minPrice=10')
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/stats - 获取网站统计', () => {
    test('获取网站统计成功', async () => {
      const res = await request(app)
        .get('/api/v1/stats')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('online_characters');
      expect(res.body.data).toHaveProperty('total_conversations');
      expect(res.body.data).toHaveProperty('user_satisfaction');
      expect(res.body.data).toHaveProperty('active_users');
    });
  });

  describe('GET /api/v1/announcements - 获取系统公告', () => {
    test('获取系统公告成功', async () => {
      const res = await request(app)
        .get('/api/v1/announcements')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);
    });
  });

  describe('GET /api/v1/help - 获取帮助文档', () => {
    test('获取帮助文档成功', async () => {
      const res = await request(app)
        .get('/api/v1/help')
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);
    });
  });
});
