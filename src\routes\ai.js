const express = require('express');
const router = express.Router();
const Joi = require('joi');
const AIController = require('../controllers/AIController');
const { authenticate } = require('../middleware/auth');
const { validate } = require('../middleware/validation');

// AI服务健康检查（公开接口）
router.get('/health',
  AIController.healthCheck
);

// 获取可用模型列表（需要认证）
router.get('/models',
  authenticate,
  AIController.getModels
);

// 获取模型定价信息（需要认证）
router.get('/pricing',
  authenticate,
  AIController.getPricing
);

// 测试AI对话（仅开发环境，需要认证）
router.post('/test',
  authenticate,
  validate(Joi.object({
    message: Joi.string().min(1).max(1000).required(),
    model: Joi.string().optional()
  })),
  AIController.testChat
);

// 获取AI服务统计信息（需要认证）
router.get('/stats',
  authenticate,
  AIController.getStats
);

// 获取用户AI使用统计（需要认证）
router.get('/user-stats',
  authenticate,
  AIController.getUserAIStats
);

module.exports = router;
