const express = require('express');
const router = express.Router();
const Joi = require('joi');
const MemoryController = require('../controllers/MemoryController');
const { validate } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');

// 所有记忆相关接口都需要认证
router.use(authenticate);

// 验证模式
const schemas = {
  // 设置偏好
  setPreference: Joi.object({
    preference_key: Joi.string().min(1).max(100).required(),
    preference_value: Joi.alternatives().try(
      Joi.string(),
      Joi.number(),
      Joi.boolean(),
      Joi.object()
    ).required(),
    preference_type: Joi.string().valid('string', 'number', 'boolean', 'json').default('string'),
    weight: Joi.number().min(0).max(1).default(1.0)
  }),

  // 批量设置偏好
  batchPreferences: Joi.object({
    preferences: Joi.object().pattern(
      Joi.string(),
      Joi.object({
        value: Joi.alternatives().try(
          Joi.string(),
          Joi.number(),
          Joi.boolean(),
          Joi.object()
        ).required(),
        type: Joi.string().valid('string', 'number', 'boolean', 'json').default('string'),
        weight: Joi.number().min(0).max(1).default(1.0)
      })
    ).required()
  }),

  // 获取记忆查询参数
  getMemories: Joi.object({
    character_id: Joi.number().integer().positive().optional(),
    memory_type: Joi.string().valid('conversation', 'preference', 'emotion', 'event', 'fact').optional(),
    importance_threshold: Joi.number().min(0).max(1).default(0),
    page: Joi.number().integer().min(1).default(1),
    pageSize: Joi.number().integer().min(1).max(100).default(20),
    include_expired: Joi.boolean().default(false)
  }),

  // 搜索记忆
  searchMemories: Joi.object({
    keyword: Joi.string().min(1).max(100).required(),
    character_id: Joi.number().integer().positive().optional(),
    memory_type: Joi.string().valid('conversation', 'preference', 'emotion', 'event', 'fact').optional(),
    emotional_tone: Joi.string().valid('positive', 'negative', 'neutral', 'mixed').optional(),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }),

  // 更新记忆重要性
  updateImportance: Joi.object({
    importance_score: Joi.number().min(0).max(1).required()
  }),

  // 添加标签
  addTags: Joi.object({
    tags: Joi.array().items(Joi.string().min(1).max(50)).min(1).max(10).required()
  }),

  // 创建记忆
  createMemory: Joi.object({
    character_id: Joi.number().integer().positive().optional(),
    memory_type: Joi.string().valid('conversation', 'preference', 'emotion', 'event', 'fact').required(),
    memory_content: Joi.string().min(1).max(2000).required(),
    memory_summary: Joi.string().max(500).optional(),
    importance_score: Joi.number().min(0).max(1).default(0.5),
    emotional_tone: Joi.string().valid('positive', 'negative', 'neutral', 'mixed').default('neutral'),
    tags: Joi.array().items(Joi.string().min(1).max(50)).max(10).optional(),
    expires_at: Joi.date().iso().optional()
  }),

  // 获取相关记忆
  getRelated: Joi.object({
    keywords: Joi.string().min(1).max(200).required(),
    limit: Joi.number().integer().min(1).max(20).default(10)
  })
};

// 用户偏好相关接口
router.get('/preferences', 
  validate(Joi.object({
    categories: Joi.string().optional()
  }), 'query'),
  (req, res, next) => MemoryController.getUserPreferences(req, res, next)
);

router.post('/preferences',
  validate(schemas.setPreference),
  (req, res, next) => MemoryController.setUserPreference(req, res, next)
);

router.post('/preferences/batch',
  validate(schemas.batchPreferences),
  (req, res, next) => MemoryController.setBatchPreferences(req, res, next)
);

router.delete('/preferences/:preference_key',
  validate(Joi.object({
    preference_key: Joi.string().min(1).max(100).required()
  }), 'params'),
  (req, res, next) => MemoryController.deleteUserPreference(req, res, next)
);

// 用户记忆相关接口
router.get('/memories',
  validate(schemas.getMemories, 'query'),
  (req, res, next) => MemoryController.getUserMemories(req, res, next)
);

router.get('/memories/search',
  validate(schemas.searchMemories, 'query'),
  (req, res, next) => MemoryController.searchMemories(req, res, next)
);

router.get('/memories/related',
  validate(schemas.getRelated, 'query'),
  (req, res, next) => MemoryController.getRelatedMemories(req, res, next)
);

router.post('/memories',
  validate(schemas.createMemory),
  (req, res, next) => MemoryController.createMemory(req, res, next)
);

router.put('/memories/:memory_id/importance',
  validate(Joi.object({
    memory_id: Joi.number().integer().positive().required()
  }), 'params'),
  validate(schemas.updateImportance),
  (req, res, next) => MemoryController.updateMemoryImportance(req, res, next)
);

router.post('/memories/:memory_id/tags',
  validate(Joi.object({
    memory_id: Joi.number().integer().positive().required()
  }), 'params'),
  validate(schemas.addTags),
  (req, res, next) => MemoryController.addMemoryTags(req, res, next)
);

router.delete('/memories/:memory_id',
  validate(Joi.object({
    memory_id: Joi.number().integer().positive().required()
  }), 'params'),
  (req, res, next) => MemoryController.deleteMemory(req, res, next)
);

// 统计和分析接口
router.get('/stats',
  validate(Joi.object({
    character_id: Joi.number().integer().positive().optional()
  }), 'query'),
  (req, res, next) => MemoryController.getMemoryStats(req, res, next)
);

router.get('/personalization',
  validate(Joi.object({
    character_id: Joi.number().integer().positive().optional()
  }), 'query'),
  (req, res, next) => MemoryController.getPersonalization(req, res, next)
);

// 维护接口
router.post('/cleanup',
  (req, res, next) => MemoryController.cleanupMemories(req, res, next)
);

module.exports = router;
