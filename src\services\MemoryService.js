const UserPreference = require('../models/UserPreference');
const UserMemory = require('../models/UserMemory');
const ChatMessage = require('../models/ChatMessage');

class MemoryService {
  constructor() {
    // 记忆类型权重
    this.memoryTypeWeights = {
      'conversation': 0.6,
      'preference': 0.8,
      'emotion': 0.7,
      'event': 0.9,
      'fact': 0.5
    };

    // 情感权重
    this.emotionalWeights = {
      'positive': 0.8,
      'negative': 0.9,
      'neutral': 0.5,
      'mixed': 0.7
    };
  }

  /**
   * 分析并存储对话记忆
   * @param {Number} userId - 用户ID
   * @param {Number} characterId - 角色ID
   * @param {String} userMessage - 用户消息
   * @param {String} aiResponse - AI回复
   * @param {Number} messageId - 消息ID
   * @returns {Object} 存储结果
   */
  async analyzeAndStoreConversation(userId, characterId, userMessage, aiResponse, messageId) {
    try {
      // 分析对话内容
      const analysis = await this.analyzeConversationContent(userMessage, aiResponse);
      
      const memories = [];
      
      // 存储重要的对话片段
      if (analysis.importance > 0.6) {
        const conversationMemory = await UserMemory.createMemory({
          user_id: userId,
          character_id: characterId,
          memory_type: 'conversation',
          memory_content: `用户: ${userMessage}\nAI: ${aiResponse}`,
          memory_summary: analysis.summary,
          importance_score: analysis.importance,
          emotional_tone: analysis.emotion,
          tags: analysis.keywords,
          related_message_id: messageId
        });
        memories.push(conversationMemory);
      }

      // 提取并存储用户偏好
      if (analysis.preferences && analysis.preferences.length > 0) {
        for (const pref of analysis.preferences) {
          await UserPreference.setUserPreference(
            userId,
            pref.key,
            pref.value,
            pref.type,
            pref.weight
          );
        }
      }

      // 存储情感状态
      if (analysis.emotion !== 'neutral') {
        const emotionMemory = await UserMemory.createMemory({
          user_id: userId,
          character_id: characterId,
          memory_type: 'emotion',
          memory_content: `用户表现出${analysis.emotion}情绪: ${analysis.emotionReason}`,
          memory_summary: `情绪状态: ${analysis.emotion}`,
          importance_score: this.emotionalWeights[analysis.emotion],
          emotional_tone: analysis.emotion,
          tags: ['emotion', analysis.emotion],
          related_message_id: messageId
        });
        memories.push(emotionMemory);
      }

      return {
        success: true,
        memories_created: memories.length,
        memories: memories,
        analysis: analysis
      };

    } catch (error) {
      console.error('Memory analysis error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 分析对话内容
   * @param {String} userMessage - 用户消息
   * @param {String} aiResponse - AI回复
   * @returns {Object} 分析结果
   */
  async analyzeConversationContent(userMessage, aiResponse) {
    // 简化的内容分析 (实际项目中可以使用NLP库或AI服务)
    const analysis = {
      importance: 0.5,
      emotion: 'neutral',
      emotionReason: '',
      summary: '',
      keywords: [],
      preferences: []
    };

    const message = userMessage.toLowerCase();
    
    // 情感分析
    if (this.containsPositiveWords(message)) {
      analysis.emotion = 'positive';
      analysis.importance += 0.2;
      analysis.emotionReason = '用户表达了积极情绪';
    } else if (this.containsNegativeWords(message)) {
      analysis.emotion = 'negative';
      analysis.importance += 0.3;
      analysis.emotionReason = '用户表达了消极情绪';
    }

    // 重要性分析
    if (this.containsImportantKeywords(message)) {
      analysis.importance += 0.3;
    }

    // 生成摘要
    analysis.summary = this.generateSummary(userMessage, aiResponse);

    // 提取关键词
    analysis.keywords = this.extractKeywords(userMessage + ' ' + aiResponse);

    // 提取偏好
    analysis.preferences = this.extractPreferences(userMessage);

    return analysis;
  }

  /**
   * 检查积极词汇
   */
  containsPositiveWords(message) {
    const positiveWords = ['开心', '高兴', '快乐', '喜欢', '爱', '好', '棒', '赞', '谢谢', '感谢'];
    return positiveWords.some(word => message.includes(word));
  }

  /**
   * 检查消极词汇
   */
  containsNegativeWords(message) {
    const negativeWords = ['难过', '伤心', '痛苦', '烦恼', '焦虑', '担心', '害怕', '生气', '愤怒', '讨厌'];
    return negativeWords.some(word => message.includes(word));
  }

  /**
   * 检查重要关键词
   */
  containsImportantKeywords(message) {
    const importantWords = ['工作', '学习', '家庭', '健康', '感情', '朋友', '梦想', '目标', '计划', '决定'];
    return importantWords.some(word => message.includes(word));
  }

  /**
   * 生成摘要
   */
  generateSummary(userMessage, aiResponse) {
    const userLength = userMessage.length;
    const aiLength = aiResponse.length;
    
    if (userLength > 100) {
      return userMessage.substring(0, 100) + '...';
    }
    
    return `用户询问关于${this.extractMainTopic(userMessage)}的问题`;
  }

  /**
   * 提取主要话题
   */
  extractMainTopic(message) {
    const topics = {
      '工作': ['工作', '职业', '上班', '同事', '老板', '公司'],
      '学习': ['学习', '考试', '学校', '老师', '同学', '课程'],
      '感情': ['感情', '恋爱', '男友', '女友', '结婚', '分手'],
      '家庭': ['家庭', '父母', '孩子', '家人', '亲戚'],
      '健康': ['健康', '身体', '生病', '医生', '锻炼', '运动'],
      '生活': ['生活', '日常', '吃饭', '睡觉', '购物', '旅行']
    };

    for (const [topic, keywords] of Object.entries(topics)) {
      if (keywords.some(keyword => message.includes(keyword))) {
        return topic;
      }
    }

    return '日常话题';
  }

  /**
   * 提取关键词
   */
  extractKeywords(text) {
    // 简化的关键词提取
    const keywords = [];
    const commonWords = ['的', '了', '是', '在', '我', '你', '他', '她', '它', '这', '那', '有', '没', '不', '很', '也', '都', '就', '还', '要', '会', '能', '可以', '应该', '觉得', '认为', '感觉'];
    
    const words = text.match(/[\u4e00-\u9fa5]+/g) || [];
    
    words.forEach(word => {
      if (word.length >= 2 && !commonWords.includes(word)) {
        keywords.push(word);
      }
    });

    return [...new Set(keywords)].slice(0, 10); // 去重并限制数量
  }

  /**
   * 提取用户偏好
   */
  extractPreferences(message) {
    const preferences = [];
    
    // 检查喜好表达
    const likePatterns = [
      { pattern: /我喜欢(.+)/, key: 'likes', type: 'string', weight: 0.8 },
      { pattern: /我不喜欢(.+)/, key: 'dislikes', type: 'string', weight: 0.8 },
      { pattern: /我的爱好是(.+)/, key: 'hobbies', type: 'string', weight: 0.9 }
    ];

    likePatterns.forEach(({ pattern, key, type, weight }) => {
      const match = message.match(pattern);
      if (match) {
        preferences.push({
          key: key,
          value: match[1].trim(),
          type: type,
          weight: weight
        });
      }
    });

    return preferences;
  }

  /**
   * 获取用户上下文记忆
   * @param {Number} userId - 用户ID
   * @param {Number} characterId - 角色ID
   * @param {String} currentMessage - 当前消息
   * @param {Number} limit - 限制数量
   * @returns {Array} 相关记忆
   */
  async getContextualMemories(userId, characterId, currentMessage, limit = 5) {
    // 提取当前消息的关键词
    const keywords = this.extractKeywords(currentMessage);
    
    // 获取相关记忆
    const relatedMemories = await UserMemory.getRelatedMemories(userId, keywords, limit);
    
    // 过滤与当前角色相关的记忆
    const characterMemories = relatedMemories.filter(memory => 
      !memory.character_id || memory.character_id === characterId
    );

    return characterMemories;
  }

  /**
   * 获取用户个性化信息
   * @param {Number} userId - 用户ID
   * @param {Number} characterId - 角色ID
   * @returns {Object} 个性化信息
   */
  async getUserPersonalization(userId, characterId) {
    // 获取用户偏好
    const preferences = await UserPreference.getUserPreferences(userId);
    
    // 获取记忆统计
    const memoryStats = await UserMemory.getMemoryStats(userId, characterId);
    
    // 获取最近的重要记忆
    const recentMemories = await UserMemory.getUserMemories(userId, {
      character_id: characterId,
      importance_threshold: 0.7,
      limit: 10
    });

    return {
      preferences: preferences,
      memory_stats: memoryStats,
      recent_important_memories: recentMemories,
      personalization_score: this.calculatePersonalizationScore(preferences, memoryStats)
    };
  }

  /**
   * 计算个性化评分
   */
  calculatePersonalizationScore(preferences, memoryStats) {
    const prefScore = Math.min(preferences.length * 0.1, 1.0);
    const memoryScore = Math.min(memoryStats.total.total_memories * 0.05, 1.0);
    const importanceScore = memoryStats.total.avg_importance || 0;
    
    return (prefScore + memoryScore + importanceScore) / 3;
  }

  /**
   * 清理用户记忆
   * @param {Number} userId - 用户ID
   * @returns {Object} 清理结果
   */
  async cleanupUserMemories(userId) {
    // 清理过期记忆
    const expiredCount = await UserMemory.cleanupExpiredMemories(userId);
    
    // 清理低重要性的旧偏好
    const oldPrefCount = await UserPreference.cleanupOldPreferences(userId, 90);
    
    return {
      expired_memories_cleaned: expiredCount,
      old_preferences_cleaned: oldPrefCount
    };
  }
}

module.exports = new MemoryService();
