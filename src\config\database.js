const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'tree_hole_chat',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 数据库表创建语句
const tableStatements = [
  // 用户表
  `CREATE TABLE IF NOT EXISTS users (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    uid VARCHAR(32) NOT NULL COMMENT '公开的用户唯一标识',
    phone_number VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    email VARCHAR(128) DEFAULT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) DEFAULT NULL COMMENT '加密后的密码',
    balance DECIMAL(18, 4) NOT NULL DEFAULT '0.0000' COMMENT '用户余额',
    role ENUM('user', 'admin') NOT NULL DEFAULT 'user' COMMENT '用户角色',
    status ENUM('active', 'frozen') NOT NULL DEFAULT 'active' COMMENT '账户状态',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_uid (uid),
    UNIQUE KEY uk_phone_number (phone_number),
    UNIQUE KEY uk_email (email)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'`,

  // AI角色表
  `CREATE TABLE IF NOT EXISTS ai_characters (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    avatar_url VARCHAR(255) DEFAULT NULL COMMENT '角色头像URL',
    description VARCHAR(500) DEFAULT NULL COMMENT '角色简介',
    system_prompt TEXT NOT NULL COMMENT '核心System Prompt',
    popularity INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '热度值',
    status ENUM('online', 'offline') NOT NULL DEFAULT 'offline' COMMENT '状态',
    sort_order INT NOT NULL DEFAULT '0' COMMENT '显示排序',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (id)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI角色表'`,

  // 聊天会话表
  `CREATE TABLE IF NOT EXISTS chat_sessions (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    character_id BIGINT UNSIGNED NOT NULL COMMENT 'AI角色ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '会话创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '会话最后活跃时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_character (user_id, character_id),
    KEY idx_user_id (user_id),
    KEY idx_character_id (character_id)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表'`,

  // 聊天消息表
  `CREATE TABLE IF NOT EXISTS chat_messages (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    session_id BIGINT UNSIGNED NOT NULL COMMENT '所属会话ID',
    sender_type ENUM('user', 'ai') NOT NULL COMMENT '发送方类型',
    content TEXT NOT NULL COMMENT '消息内容',
    prompt_tokens INT UNSIGNED NOT NULL DEFAULT '0' COMMENT 'API消耗的Prompt Tokens',
    completion_tokens INT UNSIGNED NOT NULL DEFAULT '0' COMMENT 'API消耗的Completion Tokens',
    total_tokens INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '总消耗Tokens',
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间(精确到毫秒)',
    PRIMARY KEY (id),
    KEY idx_session_id_created_at (session_id, created_at)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表'`,

  // 余额消费日志表
  `CREATE TABLE IF NOT EXISTS consumption_logs (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    message_id BIGINT UNSIGNED DEFAULT NULL COMMENT '关联的聊天消息ID',
    balance_change DECIMAL(18, 4) NOT NULL COMMENT '余额变动值(消耗为负)',
    balance_after DECIMAL(18, 4) NOT NULL COMMENT '变动后余额',
    tokens_consumed INT UNSIGNED NOT NULL COMMENT '本次消耗的Token数',
    description VARCHAR(255) DEFAULT NULL COMMENT '变动描述',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    PRIMARY KEY (id),
    KEY idx_user_id_created_at (user_id, created_at),
    KEY idx_message_id (message_id)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='余额消费日志表'`,

  // 充值套餐表
  `CREATE TABLE IF NOT EXISTS recharge_packages (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '套餐名称',
    description VARCHAR(255) DEFAULT NULL COMMENT '套餐描述',
    price DECIMAL(10, 2) NOT NULL COMMENT '售价(元)',
    balance_amount DECIMAL(18, 4) NOT NULL COMMENT '可获得的余额数量',
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'inactive' COMMENT '状态',
    sort_order INT NOT NULL DEFAULT '0' COMMENT '显示排序',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值套餐表'`,

  // 充值订单表
  `CREATE TABLE IF NOT EXISTS recharge_orders (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    order_sn VARCHAR(64) NOT NULL COMMENT '系统内部订单号',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    package_id INT UNSIGNED NOT NULL COMMENT '购买的套餐ID',
    amount_paid DECIMAL(10, 2) NOT NULL COMMENT '实际支付金额',
    balance_granted DECIMAL(18, 4) NOT NULL COMMENT '订单授予的余额',
    payment_method VARCHAR(50) DEFAULT NULL COMMENT '支付方式',
    external_txn_id VARCHAR(128) DEFAULT NULL COMMENT '支付方返回的交易流水号',
    status ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    paid_at TIMESTAMP NULL DEFAULT NULL COMMENT '支付成功时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_order_sn (order_sn),
    KEY idx_user_id (user_id),
    KEY idx_external_txn_id (external_txn_id)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值订单表'`,

  // 系统配置表
  `CREATE TABLE IF NOT EXISTS system_configs (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) DEFAULT NULL COMMENT '配置项说明',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_config_key (config_key)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表'`
];

// 初始化数据库
async function initDatabase() {
  try {
    console.log('正在初始化数据库...');

    // 1. 先连接到MySQL服务器（不指定数据库）创建数据库
    const tempConnection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password
    });

    // 创建数据库
    await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await tempConnection.end();

    // 2. 连接到目标数据库并创建表
    const connection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password,
      database: dbConfig.database
    });

    // 3. 创建表（分别执行每个CREATE TABLE语句）
    for (const statement of tableStatements) {
      if (statement.trim()) {
        await connection.execute(statement);
      }
    }

    console.log('数据库初始化完成');
    await connection.end();
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

module.exports = {
  pool,
  initDatabase
};
