const http = require('http');

console.log('🚀 开始最终API测试...\n');

// 简单的HTTP GET请求
function httpGet(path) {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:3000${path}`, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          resolve({ status: res.statusCode, data: json });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });
    req.on('error', () => resolve({ status: 0, data: null }));
  });
}

// 简单的HTTP POST请求
function httpPost(path, postData, headers = {}) {
  return new Promise((resolve) => {
    const data = JSON.stringify(postData);
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(data),
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: json });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', () => resolve({ status: 0, data: null }));
    req.write(data);
    req.end();
  });
}

async function runTests() {
  const results = [];
  
  // 测试1: 健康检查
  console.log('📋 测试健康检查...');
  const health = await httpGet('/health');
  if (health.status === 200 && health.data.status === 'ok') {
    console.log('✅ 健康检查通过');
    results.push(true);
  } else {
    console.log('❌ 健康检查失败');
    results.push(false);
  }

  // 测试2: AI角色列表
  console.log('📋 测试AI角色列表...');
  const characters = await httpGet('/api/v1/ai-characters');
  if (characters.status === 200 && characters.data.code === 0) {
    console.log(`✅ AI角色列表获取成功 (${characters.data.data.list.length}个角色)`);
    results.push(true);
  } else {
    console.log('❌ AI角色列表获取失败');
    console.log('响应:', characters.data);
    results.push(false);
  }

  // 测试3: 充值套餐
  console.log('📋 测试充值套餐...');
  const packages = await httpGet('/api/v1/recharge-packages');
  if (packages.status === 200 && packages.data.code === 0) {
    console.log(`✅ 充值套餐获取成功 (${packages.data.data.length}个套餐)`);
    results.push(true);
  } else {
    console.log('❌ 充值套餐获取失败');
    results.push(false);
  }

  // 测试4: 用户注册
  console.log('📋 测试用户注册...');
  const register = await httpPost('/api/v1/auth/register', {
    email: `test${Date.now()}@example.com`,
    password: 'password123'
  });
  
  let token = null;
  if (register.status === 200 && register.data.code === 0) {
    console.log('✅ 用户注册成功');
    token = register.data.data.access_token;
    results.push(true);
  } else {
    console.log('❌ 用户注册失败');
    console.log('响应:', register.data);
    results.push(false);
  }

  // 测试5: 获取用户信息（需要认证）
  if (token) {
    console.log('📋 测试获取用户信息...');
    const profile = await httpGet('/api/v1/user/profile', { 'Authorization': `Bearer ${token}` });
    
    // 手动实现带认证的GET请求
    const profileResult = await new Promise((resolve) => {
      const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/v1/user/profile',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const json = JSON.parse(data);
            resolve({ status: res.statusCode, data: json });
          } catch (e) {
            resolve({ status: res.statusCode, data: data });
          }
        });
      });

      req.on('error', () => resolve({ status: 0, data: null }));
      req.end();
    });

    if (profileResult.status === 200 && profileResult.data.code === 0) {
      console.log('✅ 获取用户信息成功');
      results.push(true);
    } else {
      console.log('❌ 获取用户信息失败');
      console.log('响应:', profileResult.data);
      results.push(false);
    }
  }

  // 输出结果
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 最终测试结果');
  console.log('='.repeat(60));
  console.log(`✅ 通过: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 所有核心API测试都通过了！');
    console.log('🚀 系统已准备好交付给前端团队！');
    console.log('\n📋 可用的主要接口:');
    console.log('• GET  /health - 健康检查');
    console.log('• GET  /api/v1/ai-characters - 获取AI角色列表');
    console.log('• GET  /api/v1/recharge-packages - 获取充值套餐');
    console.log('• POST /api/v1/auth/register - 用户注册');
    console.log('• POST /api/v1/auth/login - 用户登录');
    console.log('• GET  /api/v1/user/profile - 获取用户信息 (需认证)');
    console.log('• POST /api/v1/chat/messages - 发送消息 (需认证)');
    console.log('• POST /api/v1/payment/orders - 创建充值订单 (需认证)');
  } else {
    console.log(`\n💥 ${total - passed} 个测试失败，需要修复。`);
  }
}

runTests().catch(console.error);
