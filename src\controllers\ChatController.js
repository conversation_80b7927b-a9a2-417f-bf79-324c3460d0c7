const AICharacter = require('../models/AICharacter');
const ChatSession = require('../models/ChatSession');
const ChatMessage = require('../models/ChatMessage');
const ConsumptionLog = require('../models/ConsumptionLog');
const User = require('../models/User');
const { success, error, ERROR_CODES, paginated } = require('../utils/response');

class ChatController {
  /**
   * 发送消息并获取AI回复
   */
  async sendMessage(req, res, next) {
    try {
      const { userId } = req.user;
      const { character_id, content, stream = false } = req.body;

      // 验证角色是否可用
      const isCharacterAvailable = await AICharacter.isCharacterAvailable(character_id);
      if (!isCharacterAvailable) {
        return error(res, ERROR_CODES.NOT_FOUND, '角色不存在或已下线');
      }

      // 获取用户信息，检查余额
      const user = await User.findById(userId);
      if (!user) {
        return error(res, ERROR_CODES.NOT_FOUND, '用户不存在');
      }

      // TODO: 检查余额是否充足（这里需要根据预估Token数量来判断）
      // const estimatedTokens = estimateTokens(content);
      // const estimatedCost = calculateCost(estimatedTokens);
      // if (parseFloat(user.balance) < estimatedCost) {
      //   return error(res, ERROR_CODES.INSUFFICIENT_BALANCE, '余额不足，请先充值');
      // }

      // 获取或创建聊天会话
      const session = await ChatSession.getOrCreateSession(userId, character_id);

      // 保存用户消息
      const userMessage = await ChatMessage.createUserMessage(session.id, content);

      // TODO: 调用AI服务获取回复
      // 这里是核心业务逻辑，需要：
      // 1. 获取角色的system_prompt
      // 2. 获取历史对话上下文
      // 3. 构建完整的prompt
      // 4. 调用AI API
      // 5. 处理AI回复
      // 6. 计算Token消耗
      // 7. 扣除用户余额
      // 8. 记录消费日志

      // 临时模拟AI回复
      const mockAIReply = this.generateMockReply(content);
      const mockTokenUsage = {
        prompt_tokens: 50,
        completion_tokens: 30,
        total_tokens: 80
      };

      // 保存AI回复消息
      const aiMessage = await ChatMessage.createAIMessage(
        session.id,
        mockAIReply,
        mockTokenUsage
      );

      // TODO: 扣除余额和记录消费日志
      // const balanceChange = -calculateCost(mockTokenUsage.total_tokens);
      // await User.updateBalance(userId, balanceChange);
      // await ConsumptionLog.createLog({
      //   user_id: userId,
      //   message_id: aiMessage.id,
      //   balance_change: balanceChange,
      //   balance_after: parseFloat(user.balance) + parseFloat(balanceChange),
      //   tokens_consumed: mockTokenUsage.total_tokens,
      //   description: `与'${character.name}'的对话`
      // });

      // 增加角色热度
      await AICharacter.increasePopularity(character_id);

      // 返回AI回复
      success(res, {
        reply_message: {
          id: aiMessage.id,
          sender_type: 'ai',
          content: aiMessage.content,
          created_at: aiMessage.created_at
        },
        token_usage: mockTokenUsage,
        balance_change: '-0.8000', // 模拟扣费
        current_balance: user.balance // 应该是更新后的余额
      });

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取历史聊天记录
   */
  async getSessionMessages(req, res, next) {
    try {
      const { userId } = req.user;
      const { character_id } = req.params;
      const { last_message_id, limit = 20 } = req.query;

      // 验证角色是否可用
      const isCharacterAvailable = await AICharacter.isCharacterAvailable(character_id);
      if (!isCharacterAvailable) {
        return error(res, ERROR_CODES.NOT_FOUND, '角色不存在或已下线');
      }

      // 获取会话
      const session = await ChatSession.getOrCreateSession(userId, parseInt(character_id));

      // 获取消息列表
      const messages = await ChatMessage.getSessionMessages(session.id, {
        lastMessageId: last_message_id ? parseInt(last_message_id) : null,
        limit: parseInt(limit)
      });

      success(res, {
        messages,
        session_id: session.id,
        has_more: messages.length === parseInt(limit)
      });

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取会话列表
   */
  async getSessions(req, res, next) {
    try {
      const { userId } = req.user;
      const { page = 1, pageSize = 10 } = req.query;

      const result = await ChatSession.getUserSessions(userId, {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      });

      paginated(res, result.list, result.total, result.page, result.pageSize);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(req, res, next) {
    try {
      const { userId } = req.user;
      const { session_id } = req.params;

      const deleted = await ChatSession.deleteUserSession(parseInt(session_id), userId);
      if (!deleted) {
        return error(res, ERROR_CODES.NOT_FOUND, '会话不存在或无权限删除');
      }

      success(res, null, '会话删除成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取会话统计
   */
  async getSessionStats(req, res, next) {
    try {
      const { userId } = req.user;

      const stats = await ChatSession.getSessionStats(userId);
      success(res, stats);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 搜索消息内容
   */
  async searchMessages(req, res, next) {
    try {
      const { userId } = req.user;
      const { character_id } = req.params;
      const { keyword, limit = 50 } = req.query;

      if (!keyword || keyword.trim().length === 0) {
        return error(res, ERROR_CODES.BAD_REQUEST, '搜索关键词不能为空');
      }

      // 获取会话
      const session = await ChatSession.getOrCreateSession(userId, parseInt(character_id));

      // 搜索消息
      const messages = await ChatMessage.searchMessages(session.id, keyword.trim(), {
        limit: parseInt(limit)
      });

      success(res, { messages });

    } catch (err) {
      next(err);
    }
  }

  /**
   * 生成模拟AI回复（临时方法）
   */
  generateMockReply(userMessage) {
    const replies = [
      '我理解你的感受，请继续告诉我更多。',
      '这听起来确实让人困扰，你想聊聊具体的情况吗？',
      '我会一直在这里倾听你的心声。',
      '每个人都会遇到困难，你并不孤单。',
      '你的感受是完全可以理解的。',
      '让我们一起面对这个问题吧。'
    ];

    return replies[Math.floor(Math.random() * replies.length)];
  }
}

module.exports = new ChatController();
