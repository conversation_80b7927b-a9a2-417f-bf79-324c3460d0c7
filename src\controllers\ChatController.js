const AICharacter = require('../models/AICharacter');
const ChatSession = require('../models/ChatSession');
const ChatMessage = require('../models/ChatMessage');
const ConversationService = require('../services/ConversationService');
const { success, error, ERROR_CODES, paginated } = require('../utils/response');

class ChatController {
  /**
   * 发送消息并获取AI回复
   */
  async sendMessage(req, res, next) {
    try {
      const { userId } = req.user;
      const { character_id, content, stream = false, model = null } = req.body;

      // 处理流式响应
      if (stream) {
        return await this.handleStreamResponse(req, res, next);
      }

      // 调用对话服务
      const result = await ConversationService.processMessage(
        userId,
        character_id,
        content,
        { stream: false, model }
      );

      success(res, result);

    } catch (err) {
      if (err.message === 'Character not available') {
        return error(res, ERROR_CODES.NOT_FOUND, '角色不存在或已下线');
      } else if (err.message === 'Insufficient balance') {
        return error(res, ERROR_CODES.INSUFFICIENT_BALANCE, '余额不足，请先充值');
      } else if (err.message === 'Content contains inappropriate material') {
        return error(res, ERROR_CODES.BAD_REQUEST, '消息内容包含不当信息');
      } else if (err.message === 'Message too long') {
        return error(res, ERROR_CODES.BAD_REQUEST, '消息内容过长');
      } else if (err.message.includes('Rate limit exceeded')) {
        return error(res, ERROR_CODES.TOO_MANY_REQUESTS, '请求过于频繁，请稍后再试');
      }
      next(err);
    }
  }

  /**
   * 处理流式响应
   */
  async handleStreamResponse(req, res, next) {
    try {
      const { userId } = req.user;
      const { character_id, content, model = null } = req.body;

      // 设置SSE响应头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      // 调用对话服务获取流式响应
      const streamResult = await ConversationService.processMessage(
        userId,
        character_id,
        content,
        { stream: true, model }
      );

      // 发送流式数据
      for await (const chunk of streamResult) {
        const data = JSON.stringify(chunk);
        res.write(`data: ${data}\n\n`);

        if (chunk.type === 'done' || chunk.type === 'error') {
          break;
        }
      }

      res.write('data: [DONE]\n\n');
      res.end();

    } catch (err) {
      const errorData = {
        type: 'error',
        error: err.message
      };
      res.write(`data: ${JSON.stringify(errorData)}\n\n`);
      res.end();
    }
  }

  /**
   * 获取历史聊天记录
   */
  async getSessionMessages(req, res, next) {
    try {
      const { userId } = req.user;
      const { character_id } = req.params;
      const { last_message_id, limit = 20 } = req.query;

      // 验证角色是否可用
      const isCharacterAvailable = await AICharacter.isCharacterAvailable(character_id);
      if (!isCharacterAvailable) {
        return error(res, ERROR_CODES.NOT_FOUND, '角色不存在或已下线');
      }

      // 获取会话
      const session = await ChatSession.getOrCreateSession(userId, parseInt(character_id));

      // 获取消息列表
      const messages = await ChatMessage.getSessionMessages(session.id, {
        lastMessageId: last_message_id ? parseInt(last_message_id) : null,
        limit: parseInt(limit)
      });

      success(res, {
        messages,
        session_id: session.id,
        has_more: messages.length === parseInt(limit)
      });

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取会话列表
   */
  async getSessions(req, res, next) {
    try {
      const { userId } = req.user;
      const { page = 1, pageSize = 10 } = req.query;

      const result = await ChatSession.getUserSessions(userId, {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      });

      paginated(res, result.list, result.total, result.page, result.pageSize);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(req, res, next) {
    try {
      const { userId } = req.user;
      const { session_id } = req.params;

      const deleted = await ChatSession.deleteUserSession(parseInt(session_id), userId);
      if (!deleted) {
        return error(res, ERROR_CODES.NOT_FOUND, '会话不存在或无权限删除');
      }

      success(res, null, '会话删除成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取会话统计
   */
  async getSessionStats(req, res, next) {
    try {
      const { userId } = req.user;

      const stats = await ChatSession.getSessionStats(userId);
      success(res, stats);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 搜索消息内容
   */
  async searchMessages(req, res, next) {
    try {
      const { userId } = req.user;
      const { character_id } = req.params;
      const { keyword, limit = 50 } = req.query;

      if (!keyword || keyword.trim().length === 0) {
        return error(res, ERROR_CODES.BAD_REQUEST, '搜索关键词不能为空');
      }

      // 获取会话
      const session = await ChatSession.getOrCreateSession(userId, parseInt(character_id));

      // 搜索消息
      const messages = await ChatMessage.searchMessages(session.id, keyword.trim(), {
        limit: parseInt(limit)
      });

      success(res, { messages });

    } catch (err) {
      next(err);
    }
  }


}

module.exports = new ChatController();
