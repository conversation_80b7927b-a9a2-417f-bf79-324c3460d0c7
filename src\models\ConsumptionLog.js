const BaseModel = require('./BaseModel');

class ConsumptionLog extends BaseModel {
  constructor() {
    super('consumption_logs');
  }

  /**
   * 记录消费日志
   * @param {Object} logData - 日志数据
   * @returns {Object} 创建的日志记录
   */
  async createLog(logData) {
    const {
      user_id,
      message_id = null,
      balance_change,
      balance_after,
      tokens_consumed,
      description
    } = logData;

    return await this.create({
      user_id,
      message_id,
      balance_change,
      balance_after,
      tokens_consumed,
      description
    });
  }

  /**
   * 获取用户消费记录
   * @param {Number} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Object} 分页结果
   */
  async getUserConsumptionLogs(userId, options = {}) {
    const {
      page = 1,
      pageSize = 10,
      startDate = null,
      endDate = null
    } = options;

    const offset = (page - 1) * pageSize;
    let sql = `
      SELECT 
        cl.id,
        cl.balance_change,
        cl.balance_after,
        cl.tokens_consumed,
        cl.description,
        cl.created_at,
        cm.content as message_content,
        ac.name as character_name
      FROM ${this.tableName} cl
      LEFT JOIN chat_messages cm ON cl.message_id = cm.id
      LEFT JOIN chat_sessions cs ON cm.session_id = cs.id
      LEFT JOIN ai_characters ac ON cs.character_id = ac.id
      WHERE cl.user_id = ?
    `;
    const params = [userId];

    if (startDate) {
      sql += ` AND cl.created_at >= ?`;
      params.push(startDate);
    }

    if (endDate) {
      sql += ` AND cl.created_at <= ?`;
      params.push(endDate);
    }

    sql += ` ORDER BY cl.created_at DESC LIMIT ? OFFSET ?`;
    params.push(pageSize, offset);

    const countSql = `
      SELECT COUNT(*) as count 
      FROM ${this.tableName} 
      WHERE user_id = ?
      ${startDate ? ' AND created_at >= ?' : ''}
      ${endDate ? ' AND created_at <= ?' : ''}
    `;
    const countParams = [userId];
    if (startDate) countParams.push(startDate);
    if (endDate) countParams.push(endDate);

    const [list, countResult] = await Promise.all([
      this.query(sql, params),
      this.query(countSql, countParams)
    ]);

    // 格式化返回数据
    const formattedList = list.map(log => ({
      id: log.id,
      description: log.description || `与'${log.character_name || '未知角色'}'的对话`,
      balance_change: log.balance_change,
      balance_after: log.balance_after,
      tokens_consumed: log.tokens_consumed,
      created_at: log.created_at,
      message_preview: log.message_content ? 
        (log.message_content.length > 50 ? 
          log.message_content.substring(0, 50) + '...' : 
          log.message_content) : null
    }));

    return {
      list: formattedList,
      total: countResult[0].count,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };
  }

  /**
   * 获取用户消费统计
   * @param {Number} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Object} 消费统计
   */
  async getUserConsumptionStats(userId, options = {}) {
    const {
      startDate = null,
      endDate = null,
      period = 'all' // 'today', 'week', 'month', 'all'
    } = options;

    let dateCondition = '';
    const params = [userId];

    switch (period) {
      case 'today':
        dateCondition = ' AND DATE(created_at) = CURDATE()';
        break;
      case 'week':
        dateCondition = ' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case 'month':
        dateCondition = ' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      default:
        if (startDate) {
          dateCondition += ' AND created_at >= ?';
          params.push(startDate);
        }
        if (endDate) {
          dateCondition += ' AND created_at <= ?';
          params.push(endDate);
        }
    }

    const sql = `
      SELECT 
        COUNT(*) as total_records,
        SUM(ABS(balance_change)) as total_consumed,
        SUM(tokens_consumed) as total_tokens,
        AVG(ABS(balance_change)) as avg_consumption,
        MIN(created_at) as first_consumption,
        MAX(created_at) as last_consumption
      FROM ${this.tableName}
      WHERE user_id = ? AND balance_change < 0${dateCondition}
    `;

    const rows = await this.query(sql, params);
    return rows[0];
  }

  /**
   * 获取用户每日消费趋势
   * @param {Number} userId - 用户ID
   * @param {Number} days - 天数，默认30天
   * @returns {Array} 每日消费数据
   */
  async getUserDailyConsumption(userId, days = 30) {
    const sql = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as consumption_count,
        SUM(ABS(balance_change)) as daily_consumed,
        SUM(tokens_consumed) as daily_tokens
      FROM ${this.tableName}
      WHERE user_id = ? 
        AND balance_change < 0
        AND created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    return await this.query(sql, [userId, days]);
  }

  /**
   * 获取用户按角色的消费统计
   * @param {Number} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Array} 按角色的消费统计
   */
  async getUserConsumptionByCharacter(userId, options = {}) {
    const {
      startDate = null,
      endDate = null,
      limit = 10
    } = options;

    let sql = `
      SELECT 
        ac.id as character_id,
        ac.name as character_name,
        ac.avatar_url as character_avatar,
        COUNT(cl.id) as consumption_count,
        SUM(ABS(cl.balance_change)) as total_consumed,
        SUM(cl.tokens_consumed) as total_tokens,
        AVG(ABS(cl.balance_change)) as avg_consumption
      FROM ${this.tableName} cl
      JOIN chat_messages cm ON cl.message_id = cm.id
      JOIN chat_sessions cs ON cm.session_id = cs.id
      JOIN ai_characters ac ON cs.character_id = ac.id
      WHERE cl.user_id = ? AND cl.balance_change < 0
    `;
    const params = [userId];

    if (startDate) {
      sql += ` AND cl.created_at >= ?`;
      params.push(startDate);
    }

    if (endDate) {
      sql += ` AND cl.created_at <= ?`;
      params.push(endDate);
    }

    sql += `
      GROUP BY ac.id, ac.name, ac.avatar_url
      ORDER BY total_consumed DESC
      LIMIT ?
    `;
    params.push(limit);

    return await this.query(sql, params);
  }

  /**
   * 删除用户的消费记录
   * @param {Number} userId - 用户ID
   * @param {Number} days - 保留最近多少天的记录，默认90天
   * @returns {Number} 删除的记录数
   */
  async cleanupUserLogs(userId, days = 90) {
    const sql = `
      DELETE FROM ${this.tableName} 
      WHERE user_id = ? 
        AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    `;

    const result = await this.query(sql, [userId, days]);
    return result.affectedRows;
  }
}

module.exports = new ConsumptionLog();
