const Joi = require('joi');
const { error, ERROR_CODES } = require('../utils/response');

/**
 * 创建验证中间件
 * @param {Object} schema - Joi验证模式
 * @param {String} property - 验证的属性 ('body', 'query', 'params')
 */
function validate(schema, property = 'body') {
  return (req, res, next) => {
    const { error: validationError, value } = schema.validate(req[property]);
    
    if (validationError) {
      return error(res, ERROR_CODES.BAD_REQUEST, validationError.details[0].message);
    }
    
    req[property] = value;
    next();
  };
}

// 常用验证模式
const schemas = {
  // 用户注册
  register: Joi.object({
    phone_number: Joi.string().pattern(/^1[3-9]\d{9}$/).optional(),
    email: Joi.string().email().optional(),
    password: Joi.string().min(6).max(20).required(),
    verification_code: Joi.string().length(6).when('phone_number', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional()
    })
  }).or('phone_number', 'email'),

  // 用户登录
  login: Joi.object({
    identity: Joi.string().required(), // 手机号或邮箱
    password: Joi.string().required()
  }),

  // 修改密码
  changePassword: Joi.object({
    current_password: Joi.string().required(),
    new_password: Joi.string().min(6).max(20).required()
  }),

  // 发送消息
  sendMessage: Joi.object({
    character_id: Joi.number().integer().positive().required(),
    content: Joi.string().min(1).max(2000).required(),
    stream: Joi.boolean().optional().default(false),
    model: Joi.string().optional()
  }),

  // 创建充值订单
  createOrder: Joi.object({
    package_id: Joi.number().integer().positive().required(),
    payment_method: Joi.string().valid('alipay', 'wechat_pay').required()
  }),

  // 分页查询
  pagination: Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    pageSize: Joi.number().integer().min(1).max(100).optional().default(10)
  }),

  // 获取历史消息
  getMessages: Joi.object({
    last_message_id: Joi.number().integer().positive().optional(),
    limit: Joi.number().integer().min(1).max(50).optional().default(20)
  }),

  // ID参数验证
  idParam: Joi.object({
    id: Joi.number().integer().positive().required()
  }),

  // 订单号参数验证
  orderSnParam: Joi.object({
    order_sn: Joi.string().required()
  })
};

module.exports = {
  validate,
  schemas
};
