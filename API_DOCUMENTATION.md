# 🌳 树洞聊天系统 - 后端API文档

## 📋 系统概述

树洞聊天系统是一个基于DeepSeek V3 Fast的AI聊天平台，支持多角色对话、流式响应、精确计费等企业级功能。

### 🔧 技术栈
- **后端**: Node.js + Express
- **数据库**: MySQL 8.0
- **AI模型**: DeepSeek V3 Fast (671B参数，37B激活)
- **认证**: JWT Token
- **流式响应**: Server-Sent Events (SSE)

### 🌐 服务器信息
- **基础URL**: `http://localhost:3000`
- **API版本**: `v1`
- **API前缀**: `/api/v1`

## 🔐 认证机制

### JWT Token认证
所有需要认证的接口都需要在请求头中包含JWT Token：

```http
Authorization: Bearer <your_jwt_token>
```

### 获取Token
通过用户注册或登录接口获取Token：
- 注册: `POST /api/v1/auth/register`
- 登录: `POST /api/v1/auth/login`

## 📊 响应格式

### 标准响应格式
```json
{
  "code": 0,           // 0=成功，其他=错误码
  "message": "Success", // 响应消息
  "data": {}           // 业务数据
}
```

### 分页响应格式
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "list": [],        // 数据列表
    "pagination": {
      "page": 1,       // 当前页码
      "pageSize": 20,  // 每页数量
      "total": 100,    // 总记录数
      "totalPages": 5  // 总页数
    }
  }
}
```

### 错误响应格式
```json
{
  "code": 40001,
  "message": "参数验证失败",
  "data": null
}
```

## 🔗 接口列表

### 1. 认证模块 (`/api/v1/auth`)

#### 1.1 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例:**
```json
{
  "code": 0,
  "message": "注册成功",
  "data": {
    "user": {
      "id": 1,
      "uid": "u-**********",
      "email": "<EMAIL>",
      "balance": "10.0000",
      "created_at": "2024-01-20T10:30:00Z"
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 604800
  }
}
```

#### 1.2 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 1.3 用户登出 🔒
```http
POST /api/v1/auth/logout
Authorization: Bearer <token>
```

#### 1.4 刷新Token 🔒
```http
POST /api/v1/auth/refresh
Authorization: Bearer <token>
```

#### 1.5 获取当前用户信息 🔒
```http
GET /api/v1/auth/me
Authorization: Bearer <token>
```

### 2. 公开接口 (`/api/v1`)

#### 2.1 获取AI角色列表
```http
GET /api/v1/ai-characters?page=1&pageSize=20&status=online
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "温柔的倾听者",
        "avatar_url": "/avatars/listener.jpg",
        "description": "我是一个温柔耐心的倾听者...",
        "popularity": 1250,
        "status": "online",
        "sort_order": 1
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 2,
      "totalPages": 1
    }
  }
}
```

#### 2.2 获取AI角色详情
```http
GET /api/v1/ai-characters/{id}
```

#### 2.3 获取热门角色
```http
GET /api/v1/ai-characters/popular?limit=10
```

#### 2.4 搜索角色
```http
GET /api/v1/ai-characters/search?keyword=心理&page=1&pageSize=10
```

#### 2.5 获取充值套餐
```http
GET /api/v1/recharge-packages
```

**响应示例:**
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "体验包",
      "description": "新手体验，小试牛刀",
      "price": "6.00",
      "balance_amount": "6000.0000",
      "status": "active",
      "sort_order": 1
    }
  ]
}
```

#### 2.6 获取推荐套餐
```http
GET /api/v1/recharge-packages/recommended
```

### 3. AI服务模块 (`/api/v1/ai`)

#### 3.1 AI服务健康检查
```http
GET /api/v1/ai/health
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "ai_service": {
      "status": "healthy",
      "model": "DeepSeek-V3-Fast",
      "responseTime": **********
    },
    "config": {
      "default_model": "DeepSeek-V3-Fast",
      "max_tokens": 2000,
      "temperature": 0.7,
      "stream_enabled": true
    },
    "timestamp": "2024-01-20T10:30:00Z"
  }
}
```

#### 3.2 获取可用模型列表 🔒
```http
GET /api/v1/ai/models
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "models": [
      {
        "id": "DeepSeek-V3-Fast",
        "pricing": {
          "input": 0.002,
          "output": 0.008
        }
      }
    ],
    "default_model": "DeepSeek-V3-Fast",
    "supported_models": [
      "DeepSeek-V3-Fast",
      "DeepSeek-v3",
      "Qwen2.5-72B-Instruct"
    ]
  }
}
```

#### 3.3 获取模型定价信息 🔒
```http
GET /api/v1/ai/pricing
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "pricing": {
      "DeepSeek-V3-Fast": {
        "input_price_per_1k": 0.002,
        "output_price_per_1k": 0.008,
        "currency": "CNY"
      }
    },
    "billing_config": {
      "usd_to_cny_rate": 1.0,
      "service_fee_rate": 0.2,
      "min_charge_amount": 0.001
    }
  }
}
```

#### 3.4 测试AI对话 🔒 (仅开发环境)
```http
POST /api/v1/ai/test
Authorization: Bearer <token>
Content-Type: application/json

{
  "message": "你好，这是一个测试消息",
  "model": "DeepSeek-V3-Fast"
}
```

#### 3.5 获取AI服务统计 🔒
```http
GET /api/v1/ai/stats
Authorization: Bearer <token>
```

#### 3.6 获取用户AI使用统计 🔒
```http
GET /api/v1/ai/user-stats
Authorization: Bearer <token>
```

### 4. 聊天模块 (`/api/v1/chat`) 🔒

#### 4.1 发送消息并获取AI回复 🔒
```http
POST /api/v1/chat/messages
Authorization: Bearer <token>
Content-Type: application/json

{
  "character_id": 1,
  "content": "你好，我想聊聊天",
  "stream": false,
  "model": "DeepSeek-V3-Fast"
}
```

**普通响应示例:**
```json
{
  "code": 0,
  "data": {
    "success": true,
    "reply_message": {
      "id": 123,
      "sender_type": "ai",
      "content": "你好！我很高兴和你聊天...",
      "created_at": "2024-01-20T10:30:00Z"
    },
    "token_usage": {
      "promptTokens": 45,
      "completionTokens": 32,
      "totalTokens": 77
    },
    "cost": {
      "apiCost": "0.000154",
      "userCost": "0.0013",
      "currency": "CNY"
    },
    "balance_change": "-0.0013",
    "current_balance": "9.9987",
    "response_time": 1250
  }
}
```

**流式响应 (stream: true):**
```http
POST /api/v1/chat/messages
Authorization: Bearer <token>
Content-Type: application/json

{
  "character_id": 1,
  "content": "请详细介绍一下你自己",
  "stream": true,
  "model": "DeepSeek-V3-Fast"
}
```

**流式响应格式:**
```
Content-Type: text/event-stream
Cache-Control: no-cache
Connection: keep-alive

data: {"type":"content","content":"你好","accumulated":"你好"}

data: {"type":"content","content":"！我很","accumulated":"你好！我很"}

data: {"type":"done","message_id":456,"token_usage":{"promptTokens":45,"completionTokens":32,"totalTokens":77},"cost":{"apiCost":"0.000154","userCost":"0.0013","currency":"CNY"},"balance_change":"-0.0013","current_balance":"9.9987","response_time":1250,"finish_reason":"stop"}

data: [DONE]
```

#### 4.2 获取历史聊天记录 🔒
```http
GET /api/v1/chat/sessions/{character_id}/messages?page=1&pageSize=50&order=desc
Authorization: Bearer <token>
```

#### 4.3 获取会话列表 🔒
```http
GET /api/v1/chat/sessions?page=1&pageSize=20
Authorization: Bearer <token>
```

#### 4.4 删除会话 🔒
```http
DELETE /api/v1/chat/sessions/{session_id}
Authorization: Bearer <token>
```

#### 4.5 获取会话统计 🔒
```http
GET /api/v1/chat/sessions/stats
Authorization: Bearer <token>
```

#### 4.6 搜索消息内容 🔒
```http
GET /api/v1/chat/sessions/{character_id}/messages/search?keyword=关键词&limit=50
Authorization: Bearer <token>
```

### 5. 用户中心模块 (`/api/v1/user`) 🔒

#### 5.1 获取用户信息 🔒
```http
GET /api/v1/user/profile
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "uid": "u-**********",
    "phone_number": null,
    "email": "<EMAIL>",
    "balance": "9.9987",
    "total_recharge": "10.0000",
    "total_consumption": "0.0013",
    "created_at": "2024-01-20T10:30:00Z",
    "updated_at": "2024-01-20T11:00:00Z"
  }
}
```

#### 5.2 修改密码 🔒
```http
PUT /api/v1/user/password
Authorization: Bearer <token>
Content-Type: application/json

{
  "old_password": "oldpassword123",
  "new_password": "newpassword123"
}
```

#### 5.3 获取消费记录 🔒
```http
GET /api/v1/user/consumption-logs?page=1&pageSize=20&start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "balance_change": "-0.0013",
        "balance_after": "9.9987",
        "tokens_consumed": 77,
        "description": "与AI角色对话消费",
        "created_at": "2024-01-20T11:00:00Z",
        "character_name": "温柔的倾听者"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 1,
      "totalPages": 1
    },
    "summary": {
      "total_consumption": "-0.0013",
      "total_tokens": 77,
      "period_start": "2024-01-01",
      "period_end": "2024-01-31"
    }
  }
}
```

#### 5.4 获取充值记录 🔒
```http
GET /api/v1/user/recharge-orders?page=1&pageSize=20&status=completed
Authorization: Bearer <token>
```

#### 5.5 获取用户统计 🔒
```http
GET /api/v1/user/stats
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "total_messages": 156,
    "total_tokens": 12450,
    "total_spent": "15.67",
    "favorite_characters": [
      {
        "character_id": 1,
        "character_name": "温柔的倾听者",
        "message_count": 89,
        "total_tokens": 7890
      }
    ],
    "daily_usage": [
      {
        "date": "2024-01-20",
        "messages": 12,
        "tokens": 890,
        "cost": "1.23"
      }
    ],
    "this_month": {
      "messages": 45,
      "tokens": 3456,
      "cost": "4.56"
    }
  }
}
```

### 6. 支付模块 (`/api/v1/payment`)

#### 6.1 创建充值订单 🔒
```http
POST /api/v1/payment/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "package_id": 1,
  "payment_method": "alipay"
}
```

**响应示例:**
```json
{
  "code": 0,
  "data": {
    "order_sn": "R202401201030001",
    "amount": "6.00",
    "balance_amount": "6000.0000",
    "payment_method": "alipay",
    "status": "pending",
    "expires_at": "2024-01-20T11:30:00Z",
    "payment_url": "https://qr.alipay.com/bax08861...",
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

#### 6.2 查询订单状态 🔒
```http
GET /api/v1/payment/orders/{order_sn}
Authorization: Bearer <token>
```

#### 6.3 获取支付方式 🔒
```http
GET /api/v1/payment/methods
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 0,
  "data": [
    {
      "method": "alipay",
      "name": "支付宝",
      "icon": "/icons/alipay.png",
      "enabled": true,
      "fee_rate": 0.006
    },
    {
      "method": "wechat_pay",
      "name": "微信支付",
      "icon": "/icons/wechat.png",
      "enabled": true,
      "fee_rate": 0.006
    }
  ]
}
```

#### 6.4 支付宝回调 (系统接口)
```http
POST /api/v1/payment/notify/alipay
Content-Type: application/x-www-form-urlencoded

out_trade_no=R202401201030001&trade_status=TRADE_SUCCESS&...
```

#### 6.5 微信支付回调 (系统接口)
```http
POST /api/v1/payment/notify/wechat_pay
Content-Type: application/xml

<xml>
  <out_trade_no>R202401201030001</out_trade_no>
  <result_code>SUCCESS</result_code>
  ...
</xml>
```

### 7. 系统接口

#### 7.1 健康检查
```http
GET /health
```

**响应示例:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-20T10:30:00Z",
  "uptime": 3600,
  "version": "1.0.0",
  "database": "connected",
  "ai_service": "healthy"
}
```

#### 7.2 API文档
```http
GET /api/v1/docs
```

返回完整的API接口列表和说明。

## 📋 错误码说明

### 通用错误码
| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 0 | 成功 | 200 |
| 40001 | 参数验证失败 | 400 |
| 40101 | 未授权访问 | 401 |
| 40301 | 禁止访问 | 403 |
| 40401 | 资源不存在 | 404 |
| 40901 | 资源冲突 | 409 |
| 42901 | 请求过于频繁 | 429 |
| 50001 | 服务器内部错误 | 500 |
| 50301 | 外部服务错误 | 503 |

### 业务错误码
| 错误码 | 说明 |
|--------|------|
| 41001 | 用户不存在 |
| 41002 | 密码错误 |
| 41003 | 账户已被禁用 |
| 42001 | 余额不足 |
| 42002 | 充值失败 |
| 43001 | AI服务不可用 |
| 43002 | 模型不支持 |
| 43003 | Token超出限制 |

## 🚦 限流规则

### API限流
- **全局限流**: 1000请求/15分钟/IP
- **用户限流**: 100请求/分钟/用户
- **AI对话限流**: 20请求/分钟/用户
- **Token限流**: 100,000 tokens/小时/用户

### 限流响应头
```http
RateLimit-Policy: 1000;w=900
RateLimit-Limit: 1000
RateLimit-Remaining: 999
RateLimit-Reset: 837
```

### 限流超出响应
```json
{
  "code": 42901,
  "message": "请求过于频繁，请稍后再试",
  "data": {
    "retry_after": 60,
    "limit_type": "user_requests"
  }
}
```

## 🔒 安全机制

### 内容安全
- **敏感词过滤**: 自动过滤不当内容
- **输入长度限制**: 最大4000字符
- **内容审核**: 实时内容安全检查

### 访问安全
- **JWT认证**: 所有用户接口需要认证
- **Token过期**: 7天自动过期
- **IP白名单**: 支持IP访问控制
- **HTTPS强制**: 生产环境强制HTTPS

### 数据安全
- **密码加密**: bcrypt加密存储
- **敏感信息脱敏**: 日志中自动脱敏
- **SQL注入防护**: 参数化查询
- **XSS防护**: 输入输出过滤

## 💰 计费说明

### DeepSeek V3 Fast定价
- **输入Token**: ¥2/百万Token (限时五折)
- **输出Token**: ¥8/百万Token (限时五折)
- **服务费**: 20%平台服务费
- **最小扣费**: ¥0.001

### 计费公式
```
API成本 = (输入Token数/1000000) × 2 + (输出Token数/1000000) × 8
用户成本 = API成本 × (1 + 0.2)
实际扣费 = max(用户成本, 0.001)
```

### 余额管理
- **新用户赠送**: ¥10初始余额
- **充值方式**: 支付宝、微信支付
- **余额不足**: 自动停止服务
- **退款政策**: 支持7天内退款

## 🚀 性能指标

### 响应时间
- **普通接口**: < 200ms
- **AI对话**: < 2000ms
- **流式响应**: 首字符 < 1000ms
- **数据库查询**: < 100ms

### 并发能力
- **最大并发**: 1000请求/秒
- **AI并发**: 50对话/秒
- **数据库连接**: 100个连接池

### 可用性
- **系统可用性**: 99.9%
- **AI服务可用性**: 99.5%
- **数据备份**: 每日自动备份
- **故障恢复**: < 5分钟

## 🛠️ 开发工具

### 测试工具
```bash
# 健康检查
curl http://localhost:3000/health

# 获取API文档
curl http://localhost:3000/api/v1/docs

# 测试AI对话
curl -X POST http://localhost:3000/api/v1/chat/messages \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"character_id":1,"content":"你好","stream":false}'
```

### SDK支持
- **JavaScript**: 官方SDK (计划中)
- **Python**: 官方SDK (计划中)
- **Java**: 社区SDK (计划中)

## 📞 技术支持

### 联系方式
- **技术文档**: [API文档地址]
- **问题反馈**: [GitHub Issues]
- **技术交流**: [开发者群]

### 更新日志
- **v1.2.0**: DeepSeek V3 Fast集成，流式响应支持
- **v1.1.0**: 支付系统，用户中心完善
- **v1.0.0**: 基础功能，用户认证，AI对话

---

**🎉 树洞聊天系统API文档 - 支持DeepSeek V3 Fast的企业级AI聊天平台**

*最后更新: 2024-01-20*
