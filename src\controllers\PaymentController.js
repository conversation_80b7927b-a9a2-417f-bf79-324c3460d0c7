const RechargePackage = require('../models/RechargePackage');
const RechargeOrder = require('../models/RechargeOrder');
const User = require('../models/User');
const { success, error, ERROR_CODES } = require('../utils/response');

class PaymentController {
  /**
   * 创建充值订单
   */
  async createOrder(req, res, next) {
    try {
      const { userId } = req.user;
      const { package_id, payment_method } = req.body;

      // 验证套餐是否存在且可用
      const package = await RechargePackage.getActivePackageById(package_id);
      if (!package) {
        return error(res, ERROR_CODES.NOT_FOUND, '充值套餐不存在或已下架');
      }

      // 创建订单
      const order = await RechargeOrder.createOrder({
        user_id: userId,
        package_id: package.id,
        amount_paid: package.price,
        balance_granted: package.balance_amount,
        payment_method
      });

      // TODO: 根据支付方式生成支付链接
      let payUrl = '';
      switch (payment_method) {
        case 'alipay':
          payUrl = await this.generateAlipayUrl(order);
          break;
        case 'wechat_pay':
          payUrl = await this.generateWechatPayUrl(order);
          break;
        default:
          return error(res, ERROR_CODES.BAD_REQUEST, '不支持的支付方式');
      }

      success(res, {
        order_sn: order.order_sn,
        pay_url: payUrl,
        amount: order.amount_paid,
        package_name: package.name,
        expires_at: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
      }, '订单创建成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 查询订单状态
   */
  async getOrderStatus(req, res, next) {
    try {
      const { userId } = req.user;
      const { order_sn } = req.params;

      const order = await RechargeOrder.getOrderDetail(order_sn, userId);
      if (!order) {
        return error(res, ERROR_CODES.NOT_FOUND, '订单不存在');
      }

      success(res, {
        order_sn: order.order_sn,
        status: order.status,
        amount_paid: order.amount_paid,
        balance_granted: order.balance_granted,
        payment_method: order.payment_method,
        created_at: order.created_at,
        paid_at: order.paid_at,
        package_name: order.package_name
      });

    } catch (err) {
      next(err);
    }
  }

  /**
   * 支付宝支付回调
   */
  async alipayNotify(req, res, next) {
    try {
      // TODO: 实现支付宝回调处理
      // 1. 验证签名
      // 2. 验证订单信息
      // 3. 更新订单状态
      // 4. 增加用户余额
      // 5. 记录充值日志

      const { out_trade_no, trade_status, trade_no } = req.body;

      if (trade_status === 'TRADE_SUCCESS') {
        const success = await this.processPaymentSuccess(out_trade_no, trade_no, 'alipay');
        if (success) {
          res.send('success');
        } else {
          res.send('fail');
        }
      } else {
        res.send('fail');
      }

    } catch (err) {
      console.error('Alipay notify error:', err);
      res.send('fail');
    }
  }

  /**
   * 微信支付回调
   */
  async wechatNotify(req, res, next) {
    try {
      // TODO: 实现微信支付回调处理
      // 微信支付回调是XML格式，需要解析

      res.send('<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>');

    } catch (err) {
      console.error('Wechat notify error:', err);
      res.send('<xml><return_code><![CDATA[FAIL]]></return_code></xml>');
    }
  }

  /**
   * 处理支付成功逻辑
   */
  async processPaymentSuccess(orderSn, externalTxnId, paymentMethod) {
    try {
      // 查找订单
      const order = await RechargeOrder.findByOrderSn(orderSn);
      if (!order || order.status !== 'pending') {
        console.error('Order not found or already processed:', orderSn);
        return false;
      }

      // 开始数据库事务处理
      // TODO: 使用数据库事务确保数据一致性
      
      // 1. 更新订单状态
      await RechargeOrder.updateOrderStatus(orderSn, 'completed', {
        external_txn_id: externalTxnId,
        paid_at: new Date()
      });

      // 2. 增加用户余额
      await User.updateBalance(order.user_id, order.balance_granted);

      // 3. 记录充值日志（可以在ConsumptionLog中记录正数变动）
      // await ConsumptionLog.createLog({
      //   user_id: order.user_id,
      //   balance_change: order.balance_granted,
      //   balance_after: newBalance,
      //   tokens_consumed: 0,
      //   description: `充值成功 - ${order.package_name}`
      // });

      console.log(`Payment success processed for order: ${orderSn}`);
      return true;

    } catch (err) {
      console.error('Process payment success error:', err);
      return false;
    }
  }

  /**
   * 生成支付宝支付链接（模拟）
   */
  async generateAlipayUrl(order) {
    // TODO: 实现真实的支付宝支付链接生成
    // 这里返回模拟链接
    return `alipay://pay?order_sn=${order.order_sn}&amount=${order.amount_paid}`;
  }

  /**
   * 生成微信支付链接（模拟）
   */
  async generateWechatPayUrl(order) {
    // TODO: 实现真实的微信支付链接生成
    // 这里返回模拟链接
    return `weixin://pay?order_sn=${order.order_sn}&amount=${order.amount_paid}`;
  }

  /**
   * 取消订单
   */
  async cancelOrder(req, res, next) {
    try {
      const { userId } = req.user;
      const { order_sn } = req.params;

      const order = await RechargeOrder.getOrderDetail(order_sn, userId);
      if (!order) {
        return error(res, ERROR_CODES.NOT_FOUND, '订单不存在');
      }

      if (order.status !== 'pending') {
        return error(res, ERROR_CODES.BAD_REQUEST, '只能取消待支付的订单');
      }

      await RechargeOrder.updateOrderStatus(order_sn, 'cancelled');

      success(res, null, '订单取消成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取支付方式列表
   */
  async getPaymentMethods(req, res, next) {
    try {
      const paymentMethods = [
        {
          code: 'alipay',
          name: '支付宝',
          icon: '/images/alipay.png',
          enabled: true
        },
        {
          code: 'wechat_pay',
          name: '微信支付',
          icon: '/images/wechat.png',
          enabled: true
        }
      ];

      success(res, paymentMethods);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 重新支付订单
   */
  async repayOrder(req, res, next) {
    try {
      const { userId } = req.user;
      const { order_sn } = req.params;

      const order = await RechargeOrder.getOrderDetail(order_sn, userId);
      if (!order) {
        return error(res, ERROR_CODES.NOT_FOUND, '订单不存在');
      }

      if (order.status !== 'pending') {
        return error(res, ERROR_CODES.BAD_REQUEST, '订单状态不允许重新支付');
      }

      // 检查订单是否过期（30分钟）
      const orderAge = Date.now() - new Date(order.created_at).getTime();
      if (orderAge > 30 * 60 * 1000) {
        await RechargeOrder.updateOrderStatus(order_sn, 'cancelled');
        return error(res, ERROR_CODES.BAD_REQUEST, '订单已过期，请重新创建');
      }

      // 重新生成支付链接
      let payUrl = '';
      switch (order.payment_method) {
        case 'alipay':
          payUrl = await this.generateAlipayUrl(order);
          break;
        case 'wechat_pay':
          payUrl = await this.generateWechatPayUrl(order);
          break;
        default:
          return error(res, ERROR_CODES.BAD_REQUEST, '不支持的支付方式');
      }

      success(res, {
        order_sn: order.order_sn,
        pay_url: payUrl,
        amount: order.amount_paid
      });

    } catch (err) {
      next(err);
    }
  }
}

module.exports = new PaymentController();
