const MemoryService = require('../services/MemoryService');
const UserPreference = require('../models/UserPreference');
const UserMemory = require('../models/UserMemory');
const { successResponse, errorResponse } = require('../utils/response');

class MemoryController {
  async getUserPreferences(req, res, next) {
    try {
      const userId = req.user.userId;
      const { categories } = req.query;

      const categoryList = categories ? categories.split(',') : null;
      const preferences = await UserPreference.getUserPreferences(userId, categoryList);

      res.json(successResponse(preferences, 'Get preferences success'));
    } catch (error) {
      next(error);
    }
  }

  async setUserPreference(req, res, next) {
    try {
      const userId = req.user.userId;
      const { preference_key, preference_value, preference_type = 'string', weight = 1.0 } = req.body;

      const preference = await UserPreference.setUserPreference(
        userId,
        preference_key,
        preference_value,
        preference_type,
        weight
      );

      res.json(successResponse(preference, 'Set preference success'));
    } catch (error) {
      next(error);
    }
  }

  async createMemory(req, res, next) {
    try {
      const userId = req.user.userId;
      const memoryData = {
        user_id: userId,
        ...req.body
      };

      const memory = await UserMemory.createMemory(memoryData);
      res.json(successResponse(memory, 'Create memory success'));
    } catch (error) {
      next(error);
    }
  }

  async getUserMemories(req, res, next) {
    try {
      const userId = req.user.userId;
      const {
        character_id,
        memory_type,
        importance_threshold = 0,
        page = 1,
        pageSize = 20,
        include_expired = false
      } = req.query;

      const limit = parseInt(pageSize);
      const offset = (parseInt(page) - 1) * limit;

      const memories = await UserMemory.getUserMemories(userId, {
        character_id: character_id ? parseInt(character_id) : null,
        memory_type,
        importance_threshold: parseFloat(importance_threshold),
        limit,
        offset,
        include_expired: include_expired === 'true'
      });

      res.json(successResponse({
        list: memories,
        pagination: {
          page: parseInt(page),
          pageSize: limit,
          total: memories.length
        }
      }, 'Get memories success'));
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new MemoryController();