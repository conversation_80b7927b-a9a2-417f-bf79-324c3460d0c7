const MemoryService = require('../services/MemoryService');
const UserPreference = require('../models/UserPreference');
const UserMemory = require('../models/UserMemory');
const { successResponse, errorResponse } = require('../utils/response');

class MemoryController {
  /**
   * 获取用户偏好
   */
  async getUserPreferences(req, res, next) {
    try {
      const userId = req.user.id;
      const { categories } = req.query;
      
      const categoryList = categories ? categories.split(',') : null;
      const preferences = await UserPreference.getUserPreferences(userId, categoryList);
      
      res.json(successResponse(preferences, '获取用户偏好成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 设置用户偏好
   */
  async setUserPreference(req, res, next) {
    try {
      const userId = req.user.id;
      const { preference_key, preference_value, preference_type = 'string', weight = 1.0 } = req.body;
      
      const preference = await UserPreference.setUserPreference(
        userId,
        preference_key,
        preference_value,
        preference_type,
        weight
      );
      
      res.json(successResponse(preference, '设置用户偏好成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 批量设置用户偏好
   */
  async setBatchPreferences(req, res, next) {
    try {
      const userId = req.user.id;
      const { preferences } = req.body;
      
      const results = await UserPreference.setBatchPreferences(userId, preferences);
      
      res.json(successResponse(results, '批量设置偏好成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除用户偏好
   */
  async deleteUserPreference(req, res, next) {
    try {
      const userId = req.user.id;
      const { preference_key } = req.params;
      
      const success = await UserPreference.deleteUserPreference(userId, preference_key);
      
      if (success) {
        res.json(successResponse(null, '删除偏好成功'));
      } else {
        res.json(errorResponse('偏好不存在', 40401));
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户记忆
   */
  async getUserMemories(req, res, next) {
    try {
      const userId = req.user.id;
      const {
        character_id,
        memory_type,
        importance_threshold = 0,
        page = 1,
        pageSize = 20,
        include_expired = false
      } = req.query;

      const limit = parseInt(pageSize);
      const offset = (parseInt(page) - 1) * limit;

      const memories = await UserMemory.getUserMemories(userId, {
        character_id: character_id ? parseInt(character_id) : null,
        memory_type,
        importance_threshold: parseFloat(importance_threshold),
        limit,
        offset,
        include_expired: include_expired === 'true'
      });

      res.json(successResponse({
        list: memories,
        pagination: {
          page: parseInt(page),
          pageSize: limit,
          total: memories.length // 简化处理，实际应该查询总数
        }
      }, '获取用户记忆成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 搜索用户记忆
   */
  async searchMemories(req, res, next) {
    try {
      const userId = req.user.id;
      const { keyword, character_id, memory_type, emotional_tone, limit = 20 } = req.query;

      if (!keyword) {
        return res.json(errorResponse('搜索关键词不能为空', 40001));
      }

      const memories = await UserMemory.searchMemories(userId, keyword, {
        character_id: character_id ? parseInt(character_id) : null,
        memory_type,
        emotional_tone,
        limit: parseInt(limit)
      });

      res.json(successResponse(memories, '搜索记忆成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取记忆统计
   */
  async getMemoryStats(req, res, next) {
    try {
      const userId = req.user.id;
      const { character_id } = req.query;

      const memoryStats = await UserMemory.getMemoryStats(
        userId,
        character_id ? parseInt(character_id) : null
      );

      const preferenceStats = await UserPreference.getPreferenceStats(userId);

      res.json(successResponse({
        memory_stats: memoryStats,
        preference_stats: preferenceStats
      }, '获取记忆统计成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户个性化信息
   */
  async getPersonalization(req, res, next) {
    try {
      const userId = req.user.id;
      const { character_id } = req.query;

      const personalization = await MemoryService.getUserPersonalization(
        userId,
        character_id ? parseInt(character_id) : null
      );

      res.json(successResponse(personalization, '获取个性化信息成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 清理用户记忆
   */
  async cleanupMemories(req, res, next) {
    try {
      const userId = req.user.id;

      const result = await MemoryService.cleanupUserMemories(userId);

      res.json(successResponse(result, '清理记忆成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新记忆重要性
   */
  async updateMemoryImportance(req, res, next) {
    try {
      const userId = req.user.id;
      const { memory_id } = req.params;
      const { importance_score } = req.body;

      // 验证记忆是否属于当前用户
      const memory = await UserMemory.findById(memory_id);
      if (!memory || memory.user_id !== userId) {
        return res.json(errorResponse('记忆不存在', 40401));
      }

      const success = await UserMemory.updateImportance(memory_id, importance_score);

      if (success) {
        res.json(successResponse(null, '更新记忆重要性成功'));
      } else {
        res.json(errorResponse('更新失败', 50001));
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * 添加记忆标签
   */
  async addMemoryTags(req, res, next) {
    try {
      const userId = req.user.id;
      const { memory_id } = req.params;
      const { tags } = req.body;

      // 验证记忆是否属于当前用户
      const memory = await UserMemory.findById(memory_id);
      if (!memory || memory.user_id !== userId) {
        return res.json(errorResponse('记忆不存在', 40401));
      }

      const success = await UserMemory.addTags(memory_id, tags);

      if (success) {
        res.json(successResponse(null, '添加标签成功'));
      } else {
        res.json(errorResponse('添加标签失败', 50001));
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取相关记忆
   */
  async getRelatedMemories(req, res, next) {
    try {
      const userId = req.user.id;
      const { keywords, limit = 10 } = req.query;

      if (!keywords) {
        return res.json(errorResponse('关键词不能为空', 40001));
      }

      const keywordList = keywords.split(',').map(k => k.trim());
      const memories = await UserMemory.getRelatedMemories(userId, keywordList, parseInt(limit));

      res.json(successResponse(memories, '获取相关记忆成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 手动创建记忆
   */
  async createMemory(req, res, next) {
    try {
      const userId = req.user.id;
      const memoryData = {
        user_id: userId,
        ...req.body
      };

      const memory = await UserMemory.createMemory(memoryData);

      res.json(successResponse(memory, '创建记忆成功'));
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除记忆
   */
  async deleteMemory(req, res, next) {
    try {
      const userId = req.user.id;
      const { memory_id } = req.params;

      // 验证记忆是否属于当前用户
      const memory = await UserMemory.findById(memory_id);
      if (!memory || memory.user_id !== userId) {
        return res.json(errorResponse('记忆不存在', 40401));
      }

      const success = await UserMemory.deleteById(memory_id);

      if (success) {
        res.json(successResponse(null, '删除记忆成功'));
      } else {
        res.json(errorResponse('删除失败', 50001));
      }
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new MemoryController();
