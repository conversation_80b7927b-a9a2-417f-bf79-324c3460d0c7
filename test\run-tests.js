#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
  // 测试套件列表（按执行顺序）
  suites: [
    'misc.test.js',      // 基础功能测试
    'public.test.js',    // 公开接口测试
    'auth.test.js',      // 认证接口测试
    'user.test.js',      // 用户中心测试
    'chat.test.js',      // 聊天接口测试
    'payment.test.js'    // 支付接口测试
  ],
  
  // Jest配置
  jestConfig: path.join(__dirname, 'jest.config.js'),
  
  // 环境变量
  env: {
    ...process.env,
    NODE_ENV: 'test',
    DB_NAME: 'tree_hole_chat_test'
  }
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 运行单个测试套件
function runTestSuite(suiteName) {
  return new Promise((resolve, reject) => {
    colorLog('cyan', `\n📋 运行测试套件: ${suiteName}`);
    
    const testFile = path.join(__dirname, suiteName);
    const jestArgs = [
      '--config', TEST_CONFIG.jestConfig,
      '--testPathPattern', testFile,
      '--verbose',
      '--no-cache'
    ];
    
    const jest = spawn('node', ['node_modules/jest/bin/jest.js', ...jestArgs], {
      stdio: 'inherit',
      env: TEST_CONFIG.env,
      cwd: path.join(__dirname, '..')
    });
    
    jest.on('close', (code) => {
      if (code === 0) {
        colorLog('green', `✅ ${suiteName} 测试通过`);
        resolve();
      } else {
        colorLog('red', `❌ ${suiteName} 测试失败 (退出码: ${code})`);
        reject(new Error(`Test suite ${suiteName} failed with code ${code}`));
      }
    });
    
    jest.on('error', (error) => {
      colorLog('red', `❌ 运行 ${suiteName} 时发生错误: ${error.message}`);
      reject(error);
    });
  });
}

// 运行所有测试套件
async function runAllTests() {
  const startTime = Date.now();
  
  colorLog('bright', '🚀 开始运行树洞聊天系统API测试套件');
  colorLog('blue', `📊 总共 ${TEST_CONFIG.suites.length} 个测试套件`);
  
  const results = {
    passed: [],
    failed: [],
    total: TEST_CONFIG.suites.length
  };
  
  for (const suite of TEST_CONFIG.suites) {
    try {
      await runTestSuite(suite);
      results.passed.push(suite);
    } catch (error) {
      results.failed.push({ suite, error: error.message });
      
      // 如果是关键测试失败，可以选择继续或停止
      if (suite === 'misc.test.js' || suite === 'auth.test.js') {
        colorLog('yellow', '⚠️  关键测试失败，但继续运行其他测试...');
      }
    }
  }
  
  // 输出测试结果摘要
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log('\n' + '='.repeat(60));
  colorLog('bright', '📊 测试结果摘要');
  console.log('='.repeat(60));
  
  colorLog('green', `✅ 通过: ${results.passed.length}/${results.total}`);
  if (results.failed.length > 0) {
    colorLog('red', `❌ 失败: ${results.failed.length}/${results.total}`);
    console.log('\n失败的测试套件:');
    results.failed.forEach(({ suite, error }) => {
      colorLog('red', `  • ${suite}: ${error}`);
    });
  }
  
  colorLog('blue', `⏱️  总耗时: ${duration}秒`);
  
  if (results.failed.length === 0) {
    colorLog('green', '\n🎉 所有测试都通过了！API已准备好交付给前端团队。');
    process.exit(0);
  } else {
    colorLog('red', '\n💥 部分测试失败，请检查并修复问题。');
    process.exit(1);
  }
}

// 运行特定测试套件
async function runSpecificTest(suiteName) {
  if (!TEST_CONFIG.suites.includes(suiteName)) {
    colorLog('red', `❌ 测试套件 "${suiteName}" 不存在`);
    colorLog('yellow', '可用的测试套件:');
    TEST_CONFIG.suites.forEach(suite => {
      console.log(`  • ${suite}`);
    });
    process.exit(1);
  }
  
  try {
    await runTestSuite(suiteName);
    colorLog('green', `🎉 测试套件 "${suiteName}" 运行完成`);
  } catch (error) {
    colorLog('red', `💥 测试套件 "${suiteName}" 运行失败`);
    process.exit(1);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
树洞聊天系统API测试运行器

用法:
  node test/run-tests.js [选项] [测试套件名]

选项:
  --help, -h     显示帮助信息
  --list, -l     列出所有可用的测试套件

示例:
  node test/run-tests.js                    # 运行所有测试
  node test/run-tests.js auth.test.js       # 运行认证测试
  node test/run-tests.js --list             # 列出所有测试套件

可用的测试套件:
${TEST_CONFIG.suites.map(suite => `  • ${suite}`).join('\n')}
  `);
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  if (args.includes('--list') || args.includes('-l')) {
    colorLog('blue', '📋 可用的测试套件:');
    TEST_CONFIG.suites.forEach(suite => {
      console.log(`  • ${suite}`);
    });
    return;
  }
  
  if (args.length === 1) {
    await runSpecificTest(args[0]);
  } else {
    await runAllTests();
  }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  colorLog('red', '❌ 未处理的Promise拒绝:');
  console.error(reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  colorLog('red', '❌ 未捕获的异常:');
  console.error(error);
  process.exit(1);
});

// 运行主函数
main().catch(error => {
  colorLog('red', `❌ 运行测试时发生错误: ${error.message}`);
  process.exit(1);
});
