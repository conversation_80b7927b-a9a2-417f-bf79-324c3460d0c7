const User = require('../models/User');
const { generateToken } = require('../utils/jwt');
const { success, error, ERROR_CODES } = require('../utils/response');

class AuthController {
  /**
   * 用户注册
   */
  async register(req, res, next) {
    try {
      const { phone_number, email, password, verification_code } = req.body;

      // 检查用户是否已存在
      const existingUser = await User.checkUserExists(phone_number, email);
      if (existingUser) {
        return error(res, ERROR_CODES.CONFLICT, '手机号或邮箱已被注册');
      }

      // TODO: 验证手机验证码（如果提供了手机号）
      if (phone_number && verification_code) {
        // 这里应该验证验证码的逻辑
        // const isValidCode = await verifyCode(phone_number, verification_code);
        // if (!isValidCode) {
        //   return error(res, ERROR_CODES.BAD_REQUEST, '验证码错误或已过期');
        // }
      }

      // 创建用户
      const user = await User.createUser({
        phone_number,
        email,
        password
      });

      // 生成JWT Token
      const token = generateToken({
        userId: user.id,
        uid: user.uid,
        role: user.role
      });

      // 返回用户信息和Token
      const publicUserInfo = User.getPublicInfo(user);
      success(res, {
        user: publicUserInfo,
        access_token: token
      }, '注册成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 用户登录
   */
  async login(req, res, next) {
    try {
      const { identity, password } = req.body;

      // 查找用户
      const user = await User.findByIdentity(identity);
      if (!user) {
        return error(res, ERROR_CODES.INVALID_CREDENTIALS, '用户名或密码错误');
      }

      // 检查账户状态
      if (user.status !== 'active') {
        return error(res, ERROR_CODES.FORBIDDEN, '账户已被冻结，请联系客服');
      }

      // 验证密码
      const isValidPassword = await User.verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        return error(res, ERROR_CODES.INVALID_CREDENTIALS, '用户名或密码错误');
      }

      // 生成JWT Token
      const token = generateToken({
        userId: user.id,
        uid: user.uid,
        role: user.role
      });

      success(res, {
        access_token: token,
        user: User.getPublicInfo(user)
      }, '登录成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 用户登出
   */
  async logout(req, res, next) {
    try {
      // JWT是无状态的，客户端删除Token即可
      // 这里可以记录登出日志或做其他处理
      success(res, null, '登出成功');
    } catch (err) {
      next(err);
    }
  }

  /**
   * 刷新Token
   */
  async refreshToken(req, res, next) {
    try {
      const { userId, uid, role } = req.user;

      // 验证用户是否仍然有效
      const user = await User.findById(userId);
      if (!user || user.status !== 'active') {
        return error(res, ERROR_CODES.UNAUTHORIZED, '用户状态异常，请重新登录');
      }

      // 生成新的Token
      const token = generateToken({
        userId: user.id,
        uid: user.uid,
        role: user.role
      });

      success(res, {
        access_token: token
      }, 'Token刷新成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(req, res, next) {
    try {
      const { userId } = req.user;

      const user = await User.findById(userId);
      if (!user) {
        return error(res, ERROR_CODES.NOT_FOUND, '用户不存在');
      }

      success(res, User.getPublicInfo(user));

    } catch (err) {
      next(err);
    }
  }

  /**
   * 发送验证码（预留接口）
   */
  async sendVerificationCode(req, res, next) {
    try {
      const { phone_number, type = 'register' } = req.body;

      // TODO: 实现发送验证码逻辑
      // 1. 验证手机号格式
      // 2. 检查发送频率限制
      // 3. 生成验证码
      // 4. 调用短信服务发送
      // 5. 存储验证码（Redis）

      success(res, null, '验证码发送成功');

    } catch (err) {
      next(err);
    }
  }
}

module.exports = new AuthController();
