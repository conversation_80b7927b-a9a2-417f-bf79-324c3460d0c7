const express = require('express');
const router = express.Router();
const Joi = require('joi');
const AuthController = require('../controllers/AuthController');
const { authenticate } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// 用户注册
router.post('/register',
  validate(schemas.register),
  AuthController.register
);

// 用户登录
router.post('/login',
  validate(schemas.login),
  AuthController.login
);

// 用户登出
router.post('/logout',
  authenticate,
  AuthController.logout
);

// 刷新Token
router.post('/refresh',
  authenticate,
  AuthController.refreshToken
);

// 获取当前用户信息
router.get('/me',
  authenticate,
  AuthController.getCurrentUser
);

// 发送验证码
router.post('/send-code',
  validate(Joi.object({
    phone_number: Joi.string().pattern(/^1[3-9]\d{9}$/).required(),
    type: Joi.string().valid('register', 'login', 'bind').optional().default('register')
  })),
  AuthController.sendVerificationCode
);

module.exports = router;
