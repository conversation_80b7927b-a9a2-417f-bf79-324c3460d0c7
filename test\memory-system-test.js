const http = require('http');

console.log('🧠 长期记忆系统测试...\n');

// HTTP请求工具
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(body);
          resolve({ status: res.statusCode, data: json });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', () => resolve({ status: 0, data: null }));

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testMemorySystem() {
  console.log('📋 测试长期记忆系统功能...\n');

  // 1. 注册用户获取token
  console.log('🔐 注册测试用户...');
  const registerResult = await makeRequest('POST', '/api/v1/auth/register', {
    email: `memory_test_${Date.now()}@example.com`,
    password: 'password123'
  });

  if (registerResult.status !== 200 || registerResult.data.code !== 0) {
    console.log('❌ 用户注册失败:', registerResult.data);
    return;
  }

  const token = registerResult.data.data.access_token;
  const authHeaders = { 'Authorization': `Bearer ${token}` };
  console.log('✅ 用户注册成功\n');

  // 2. 测试用户偏好管理
  console.log('🎯 测试用户偏好管理...');
  
  // 设置单个偏好
  const setPrefResult = await makeRequest('POST', '/api/v1/memory/preferences', {
    preference_key: 'favorite_topic',
    preference_value: '心理健康',
    preference_type: 'string',
    weight: 0.9
  }, authHeaders);

  if (setPrefResult.status === 200 && setPrefResult.data.code === 0) {
    console.log('✅ 设置单个偏好成功');
  } else {
    console.log('❌ 设置偏好失败:', setPrefResult.data);
  }

  // 批量设置偏好
  const batchPrefResult = await makeRequest('POST', '/api/v1/memory/preferences/batch', {
    preferences: {
      'communication_style': {
        value: '温柔耐心',
        type: 'string',
        weight: 0.8
      },
      'preferred_time': {
        value: 'evening',
        type: 'string',
        weight: 0.6
      },
      'age_range': {
        value: 25,
        type: 'number',
        weight: 0.7
      }
    }
  }, authHeaders);

  if (batchPrefResult.status === 200 && batchPrefResult.data.code === 0) {
    console.log('✅ 批量设置偏好成功');
  } else {
    console.log('❌ 批量设置偏好失败:', batchPrefResult.data);
  }

  // 获取用户偏好
  const getPrefResult = await makeRequest('GET', '/api/v1/memory/preferences', null, authHeaders);
  if (getPrefResult.status === 200 && getPrefResult.data.code === 0) {
    console.log('✅ 获取偏好成功，共', getPrefResult.data.data.length, '个偏好');
    getPrefResult.data.data.forEach(pref => {
      console.log(`   ${pref.preference_key}: ${pref.preference_value} (权重: ${pref.weight})`);
    });
  } else {
    console.log('❌ 获取偏好失败:', getPrefResult.data);
  }

  // 3. 测试记忆创建和管理
  console.log('\n💭 测试记忆创建和管理...');

  // 创建记忆
  const createMemoryResult = await makeRequest('POST', '/api/v1/memory/memories', {
    character_id: 1,
    memory_type: 'conversation',
    memory_content: '用户提到了对心理健康话题的兴趣，特别关注焦虑和压力管理',
    memory_summary: '用户关注心理健康，焦虑管理',
    importance_score: 0.8,
    emotional_tone: 'neutral',
    tags: ['心理健康', '焦虑', '压力管理']
  }, authHeaders);

  let memoryId = null;
  if (createMemoryResult.status === 200 && createMemoryResult.data.code === 0) {
    console.log('✅ 创建记忆成功');
    memoryId = createMemoryResult.data.data.id;
  } else {
    console.log('❌ 创建记忆失败:', createMemoryResult.data);
  }

  // 创建更多记忆用于测试
  const memories = [
    {
      memory_type: 'emotion',
      memory_content: '用户表现出对工作压力的担忧',
      memory_summary: '工作压力担忧',
      importance_score: 0.7,
      emotional_tone: 'negative',
      tags: ['工作', '压力', '担忧']
    },
    {
      memory_type: 'preference',
      memory_content: '用户喜欢在晚上进行深度对话',
      memory_summary: '偏好晚上对话',
      importance_score: 0.6,
      emotional_tone: 'neutral',
      tags: ['时间偏好', '深度对话']
    }
  ];

  for (const memory of memories) {
    await makeRequest('POST', '/api/v1/memory/memories', {
      character_id: 1,
      ...memory
    }, authHeaders);
  }

  // 获取用户记忆
  const getMemoriesResult = await makeRequest('GET', '/api/v1/memory/memories?character_id=1', null, authHeaders);
  if (getMemoriesResult.status === 200 && getMemoriesResult.data.code === 0) {
    console.log('✅ 获取记忆成功，共', getMemoriesResult.data.data.list.length, '条记忆');
  } else {
    console.log('❌ 获取记忆失败:', getMemoriesResult.data);
  }

  // 4. 测试记忆搜索
  console.log('\n🔍 测试记忆搜索...');
  const searchResult = await makeRequest('GET', '/api/v1/memory/memories/search?keyword=' + encodeURIComponent('心理健康'), null, authHeaders);
  if (searchResult.status === 200 && searchResult.data.code === 0) {
    console.log('✅ 搜索记忆成功，找到', searchResult.data.data.length, '条相关记忆');
  } else {
    console.log('❌ 搜索记忆失败:', searchResult.data);
  }

  // 5. 测试相关记忆获取
  console.log('\n🔗 测试相关记忆获取...');
  const relatedResult = await makeRequest('GET', '/api/v1/memory/memories/related?keywords=' + encodeURIComponent('压力,焦虑'), null, authHeaders);
  if (relatedResult.status === 200 && relatedResult.data.code === 0) {
    console.log('✅ 获取相关记忆成功，找到', relatedResult.data.data.length, '条相关记忆');
  } else {
    console.log('❌ 获取相关记忆失败:', relatedResult.data);
  }

  // 6. 测试记忆统计
  console.log('\n📊 测试记忆统计...');
  const statsResult = await makeRequest('GET', '/api/v1/memory/stats?character_id=1', null, authHeaders);
  if (statsResult.status === 200 && statsResult.data.code === 0) {
    console.log('✅ 获取记忆统计成功');
    console.log('   总记忆数:', statsResult.data.data.memory_stats.total.total_memories);
    console.log('   平均重要性:', statsResult.data.data.memory_stats.total.avg_importance);
    console.log('   偏好总数:', statsResult.data.data.preference_stats.total_preferences);
  } else {
    console.log('❌ 获取记忆统计失败:', statsResult.data);
  }

  // 7. 测试个性化信息
  console.log('\n🎨 测试个性化信息...');
  const personalizationResult = await makeRequest('GET', '/api/v1/memory/personalization?character_id=1', null, authHeaders);
  if (personalizationResult.status === 200 && personalizationResult.data.code === 0) {
    console.log('✅ 获取个性化信息成功');
    console.log('   个性化评分:', personalizationResult.data.data.personalization_score.toFixed(2));
    console.log('   偏好数量:', personalizationResult.data.data.preferences.length);
    console.log('   重要记忆数量:', personalizationResult.data.data.recent_important_memories.length);
  } else {
    console.log('❌ 获取个性化信息失败:', personalizationResult.data);
  }

  // 8. 测试记忆更新功能
  if (memoryId) {
    console.log('\n✏️ 测试记忆更新功能...');
    
    // 更新重要性
    const updateImportanceResult = await makeRequest('PUT', `/api/v1/memory/memories/${memoryId}/importance`, {
      importance_score: 0.9
    }, authHeaders);
    
    if (updateImportanceResult.status === 200 && updateImportanceResult.data.code === 0) {
      console.log('✅ 更新记忆重要性成功');
    } else {
      console.log('❌ 更新记忆重要性失败:', updateImportanceResult.data);
    }

    // 添加标签
    const addTagsResult = await makeRequest('POST', `/api/v1/memory/memories/${memoryId}/tags`, {
      tags: ['重要', '核心话题']
    }, authHeaders);
    
    if (addTagsResult.status === 200 && addTagsResult.data.code === 0) {
      console.log('✅ 添加记忆标签成功');
    } else {
      console.log('❌ 添加记忆标签失败:', addTagsResult.data);
    }
  }

  // 9. 测试AI对话中的记忆集成
  console.log('\n🤖 测试AI对话中的记忆集成...');
  const chatResult = await makeRequest('POST', '/api/v1/chat/messages', {
    character_id: 1,
    content: '我最近工作压力很大，感到很焦虑，你能给我一些建议吗？',
    stream: false,
    model: 'DeepSeek-V3-Fast'
  }, authHeaders);

  if (chatResult.status === 200 && chatResult.data.code === 0) {
    console.log('✅ AI对话成功，记忆系统已集成');
    console.log('   AI回复长度:', chatResult.data.data.reply_message.content.length);
    console.log('   Token使用:', chatResult.data.data.token_usage.totalTokens);
  } else {
    console.log('❌ AI对话失败:', chatResult.data);
  }

  console.log('\n' + '='.repeat(60));
  console.log('🧠 长期记忆系统测试完成');
  console.log('='.repeat(60));
  
  console.log('✅ 测试完成！长期记忆系统功能验证：');
  console.log('• 🎯 用户偏好管理 - 支持个性化设置');
  console.log('• 💭 记忆创建和存储 - 自动分析对话内容');
  console.log('• 🔍 智能记忆搜索 - 基于关键词和标签');
  console.log('• 🔗 相关记忆关联 - 上下文感知推荐');
  console.log('• 📊 记忆统计分析 - 数据洞察报告');
  console.log('• 🎨 个性化信息 - 用户画像构建');
  console.log('• 🤖 AI对话集成 - 记忆增强的智能回复');
  
  console.log('\n🎉 长期记忆系统已成功集成到树洞聊天系统！');
}

testMemorySystem().catch(console.error);
