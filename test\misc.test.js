const { app, request } = require('./setup');

describe('其他接口测试', () => {
  describe('GET /health - 健康检查', () => {
    test('健康检查成功', async () => {
      const res = await request(app)
        .get('/health')
        .expect(200);

      expect(res.body).toHaveProperty('status');
      expect(res.body).toHaveProperty('timestamp');
      expect(res.body).toHaveProperty('version');
      expect(res.body.status).toBe('ok');
    });
  });

  describe('GET /api/v1/docs - API文档', () => {
    test('开发环境获取API文档成功', async () => {
      // 设置为开发环境
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const res = await request(app)
        .get('/api/v1/docs')
        .expect(200);

      expect(res.body).toHaveProperty('title');
      expect(res.body).toHaveProperty('version');
      expect(res.body).toHaveProperty('baseUrl');
      expect(res.body).toHaveProperty('endpoints');
      expect(res.body).toHaveProperty('authentication');
      expect(res.body).toHaveProperty('responseFormat');

      // 检查接口分类
      expect(res.body.endpoints).toHaveProperty('auth');
      expect(res.body.endpoints).toHaveProperty('public');
      expect(res.body.endpoints).toHaveProperty('chat');
      expect(res.body.endpoints).toHaveProperty('user');
      expect(res.body.endpoints).toHaveProperty('payment');

      // 恢复环境变量
      process.env.NODE_ENV = originalEnv;
    });

    test('生产环境API文档不可访问', async () => {
      // 设置为生产环境
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const res = await request(app)
        .get('/api/v1/docs')
        .expect(404);

      expect(res.body.message).toBe('Not found');

      // 恢复环境变量
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('404处理', () => {
    test('不存在的路由返回404', async () => {
      const res = await request(app)
        .get('/api/v1/nonexistent')
        .expect(404);

      expect(res.body.code).toBe(40401);
      expect(res.body.message).toContain('不存在');
    });

    test('不存在的方法返回404', async () => {
      const res = await request(app)
        .patch('/api/v1/ai-characters')
        .expect(404);

      expect(res.body.code).toBe(40401);
    });
  });

  describe('限流测试', () => {
    test('正常请求不被限流', async () => {
      const res = await request(app)
        .get('/health')
        .expect(200);

      expect(res.body.status).toBe('ok');
    });

    // 注意：实际的限流测试需要发送大量请求，这里只做基础测试
    test('请求头包含限流信息', async () => {
      const res = await request(app)
        .get('/health')
        .expect(200);

      // 检查是否有限流相关的响应头
      expect(res.headers).toHaveProperty('x-ratelimit-limit');
      expect(res.headers).toHaveProperty('x-ratelimit-remaining');
    });
  });

  describe('CORS测试', () => {
    test('OPTIONS请求支持CORS', async () => {
      const res = await request(app)
        .options('/api/v1/ai-characters')
        .set('Origin', 'http://localhost:3001')
        .expect(204);

      expect(res.headers).toHaveProperty('access-control-allow-origin');
    });

    test('GET请求包含CORS头', async () => {
      const res = await request(app)
        .get('/api/v1/ai-characters')
        .set('Origin', 'http://localhost:3001')
        .expect(200);

      expect(res.headers).toHaveProperty('access-control-allow-origin');
    });
  });

  describe('安全头测试', () => {
    test('响应包含安全头', async () => {
      const res = await request(app)
        .get('/health')
        .expect(200);

      // Helmet设置的安全头
      expect(res.headers).toHaveProperty('x-content-type-options');
      expect(res.headers).toHaveProperty('x-frame-options');
      expect(res.headers).toHaveProperty('x-xss-protection');
    });
  });

  describe('请求体大小限制', () => {
    test('正常大小的请求体', async () => {
      const normalData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const res = await request(app)
        .post('/api/v1/auth/register')
        .send(normalData);

      // 不管是否成功注册，至少不应该因为请求体大小被拒绝
      expect(res.status).not.toBe(413);
    });

    // 注意：测试超大请求体可能会影响测试性能，这里省略
  });

  describe('Content-Type处理', () => {
    test('JSON Content-Type正确处理', async () => {
      const res = await request(app)
        .post('/api/v1/auth/register')
        .set('Content-Type', 'application/json')
        .send(JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        }));

      // 应该能正确解析JSON
      expect(res.status).not.toBe(400);
    });

    test('无效Content-Type处理', async () => {
      const res = await request(app)
        .post('/api/v1/auth/register')
        .set('Content-Type', 'text/plain')
        .send('invalid data');

      // 应该返回400错误
      expect(res.status).toBe(400);
    });
  });

  describe('错误处理', () => {
    test('参数验证错误格式正确', async () => {
      const res = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: 'invalid-email',
          password: '123'
        })
        .expect(400);

      expect(res.body).toHaveProperty('code');
      expect(res.body).toHaveProperty('message');
      expect(res.body).toHaveProperty('data');
      expect(res.body.code).toBe(40001);
      expect(res.body.data).toBe(null);
    });

    test('认证错误格式正确', async () => {
      const res = await request(app)
        .get('/api/v1/user/profile')
        .expect(401);

      expect(res.body.code).toBe(40101);
      expect(res.body.data).toBe(null);
    });
  });
});
