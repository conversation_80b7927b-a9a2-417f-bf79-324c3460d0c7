const request = require('supertest');
const app = require('../src/app');
const { pool } = require('../src/config/database');

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:3000',
  timeout: 10000
};

// 测试用户数据
const TEST_USERS = {
  user1: {
    email: '<EMAIL>',
    password: 'password123',
    phone_number: '13800138001'
  },
  user2: {
    email: '<EMAIL>', 
    password: 'password456',
    phone_number: '13800138002'
  }
};

// 全局变量存储测试过程中的数据
global.testData = {
  tokens: {},
  users: {},
  characters: [],
  packages: [],
  sessions: {},
  orders: {}
};

// 测试工具函数
const testUtils = {
  // 创建测试用户并获取token
  async createUserAndGetToken(userData, userKey = 'user1') {
    const registerRes = await request(app)
      .post('/api/v1/auth/register')
      .send(userData)
      .expect(200);

    expect(registerRes.body.code).toBe(0);
    expect(registerRes.body.data.access_token).toBeDefined();
    
    global.testData.tokens[userKey] = registerRes.body.data.access_token;
    global.testData.users[userKey] = registerRes.body.data.user;
    
    return {
      token: registerRes.body.data.access_token,
      user: registerRes.body.data.user
    };
  },

  // 用户登录获取token
  async loginAndGetToken(credentials, userKey = 'user1') {
    const loginRes = await request(app)
      .post('/api/v1/auth/login')
      .send({
        identity: credentials.email,
        password: credentials.password
      })
      .expect(200);

    expect(loginRes.body.code).toBe(0);
    expect(loginRes.body.data.access_token).toBeDefined();
    
    global.testData.tokens[userKey] = loginRes.body.data.access_token;
    global.testData.users[userKey] = loginRes.body.data.user;
    
    return {
      token: loginRes.body.data.access_token,
      user: loginRes.body.data.user
    };
  },

  // 获取认证头
  getAuthHeader(userKey = 'user1') {
    const token = global.testData.tokens[userKey];
    return token ? { Authorization: `Bearer ${token}` } : {};
  },

  // 清理测试数据
  async cleanupTestData() {
    try {
      // 删除测试用户创建的数据
      const testEmails = Object.values(TEST_USERS).map(u => u.email);
      const testPhones = Object.values(TEST_USERS).map(u => u.phone_number);
      
      if (testEmails.length > 0) {
        // 删除测试用户的相关数据
        await pool.execute(
          'DELETE FROM consumption_logs WHERE user_id IN (SELECT id FROM users WHERE email IN (?))',
          [testEmails]
        );
        
        await pool.execute(
          'DELETE FROM recharge_orders WHERE user_id IN (SELECT id FROM users WHERE email IN (?))',
          [testEmails]
        );
        
        await pool.execute(
          'DELETE FROM chat_messages WHERE session_id IN (SELECT id FROM chat_sessions WHERE user_id IN (SELECT id FROM users WHERE email IN (?)))',
          [testEmails]
        );
        
        await pool.execute(
          'DELETE FROM chat_sessions WHERE user_id IN (SELECT id FROM users WHERE email IN (?))',
          [testEmails]
        );
        
        await pool.execute(
          'DELETE FROM users WHERE email IN (?) OR phone_number IN (?)',
          [testEmails, testPhones]
        );
      }
    } catch (error) {
      console.warn('清理测试数据时出错:', error.message);
    }
  },

  // 等待函数
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
};

// 设置测试超时
jest.setTimeout(TEST_CONFIG.timeout);

// 导出配置和工具
module.exports = {
  app,
  request,
  TEST_CONFIG,
  TEST_USERS,
  testUtils
};
