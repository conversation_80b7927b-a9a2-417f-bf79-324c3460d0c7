DeepSeek V3 Fast
限时五折（5.13～7.31）
创建于 2025 年 5 月 13 日
32k context
¥4¥2/M input tokens
¥16¥8/M output tokens
DeepSeek V3 Fast 是 DeepSeek V3 0324 版本的高TPS极速版，满血非量化，代码与数学能力更强，响应更快！

DeepSeek V3 Fast
DeepSeek V3 Fast 是 DeepSeek V3 0324 版本的高TPS极速版，满血非量化，代码与数学能力更强，响应更快！

DeepSeek V3 0324 是一个强大的专家混合模型(MoE)，总参数量达671B，每个token激活37B参数。
采用多头潜在注意力(MLA)和DeepSeekMoE架构，实现高效推理和经济的训练成本。
创新性地采用无辅助损失的负载均衡策略，并设置多token预测训练目标以提升性能。
在14.8万亿多样化、高质量的token上进行预训练，并通过监督微调和强化学习阶段充分发挥其能力。
全面评估显示，DeepSeek-V3超越其他开源模型，性能可与领先的闭源模型相媲美。
仅需2.788M H800 GPU小时即可完成全部训练，且训练过程极其稳定，无不可恢复的损失峰值或回滚。
Context
32K
Max Output
32K
Input
¥4 ¥2
Output
¥16 ¥8
Latency
1.46s
Throughput
150t/s、

文档
API使用
Chat completions
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/v1/chat/completions

请求方式：POST

Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
Body参数
名称	类型	必填	描述
messages	array(message)	是	聊天上下文信息。支持Qwen VL系列模型。
纯文本示例：messages=[{"role": "system", "content": "You are a helpful assistant."},{"role": "user", "content": "Knock knock."},{"role": "assistant", "content": "Who's there?"},{"role": "user", "content": "Orange."},]
多模态示例：{"messages":[{"role":"user","content":[{"type":"text","text":"describe the image in 100 words or less"},{"type":"image_url","image_url":{"url":"xxx","detail":"high"}}]}]}}
model	string	是	模型
tools	array	否	工具列表，只支持function，支持的模型有DeepSeek-R1，DeepSeek-v3，QwQ-32B，Qwen3-235B-A22B，Qwen3-14B, Kimi-K2
stream	bool	否	是否以流式接口的形式返回数据，默认false
max_tokens	integer	否	模型回复最大长度（单位 token）
temperature	number	否	较高的数值会使输出更加随机，而较低的数值会使其更加集中。默认值1.0，取值范围[0,2.0]。支持的模型有DeepSeek-Prover-V2, DeepSeek-V3-Fast, DeepSeek-V3, Qwen全系列
top_p	number	否	影响输出文本的多样性，取值越大，生成文本的多样性越强。默认值1.0。支持的模型有DeepSeek-Prover-V2, DeepSeek-V3-Fast, DeepSeek-v3, Qwen全系列
stop	array(string)	否	停止生成更多Tokens的最多4个字符串。支持的模型有DeepSeek-Prover-V2, DeepSeek-V3-Fast, DeepSeek-v3, Qwen全系列
presence_penalty	number	否	通过对已生成的token增加惩罚，减少重复生成的现象。默认值0，取值范围：[-2.0, 2.0]。支持的模型有DeepSeek-Prover-V2, DeepSeek-V3-Fast, DeepSeek-v3, Qwen全系列
frequency_penalty	number	否	根据新词在当前文本中的频率进行惩罚，降低模型逐字重复同一行的可能性。 默认值0，取值范围：[-2.0, 2.0]。支持的模型有DeepSeek-Prover-V2, DeepSeek-V3-Fast, DeepSeek-v3
logprobs	boolean	否	默认值false。是否返回输出 tokens 的对数概率。支持的模型有DeepSeek-Prover-V2, DeepSeek-V3-Fast, DeepSeek-v3
top_logprobs	integer	否	默认值0，取值范围为 [0, 20]。指定每个输出 token 位置最有可能返回的 token 数量，每个 token 都有关联的对数概率。仅当 logprobs为true 时可以设置 top_logprobs 参数。支持的模型有DeepSeek-Prover-V2, DeepSeek-V3-Fast, DeepSeek-v3
response_format	object	否	指定模型必须输出的格式的对象。
默认值： { "type": "text" }
设置为 { "type": "json_object" } 可启用 JSON 模式，这保证模型生成的消息是有效的 JSON。
重要：使用 JSON 模式时，您还必须通过系统或用户消息提示模型自行生成JSON。支持的模型有DeepSeek-v3
响应说明
响应头参数
名称	值	描述
Content-Type	流式：text/event-stream非流式：application/json	
响应体参数
a.非流式

名称	类型	描述
object	string	回包类型 chat.completion.chunk：多轮对话返回
created	int	时间戳
model	string	模型
示例值：Qwen/Qwen2.5-72B-Instruct
choices	array	
choices[0].index	int	索引
choices[0].finish_reason	string	结束原因 正常结束：stop，token超长截断结束：length
choices[0].message	object	模型回答
choices[0].message.tool_calls	array	工具列表
choices[0].message.tool_calls[0].function	object	函数调用信息
choices[0].refs	array	引用列表，调用自定义模型且模型输出包含文档引用时存在。在非流式调用中，会在最终结果内输出此次回答包含的所有引用来源信息。
choices[0].refs[0].index	int	引用来源出现顺序
choices[0].refs[0].title	string	引用数据标题
choices[0].refs[0].content	string	引用数据内容
choices[0].refs[0].type	string	引用数据类型，file/qa/web， 分别代表文件知识， Q&A Table和web搜索
choices[0].refs[0].url	string	引用数据url，其中，file和qa数据url访问需配置apikey，web数据url访问无需配置apikey
b.流式

名称	类型	描述
object	string	回包类型 chat.completion.chunk：多轮对话返回
created	int	时间戳
model	string	模型
示例值：Qwen/Qwen2.5-72B-Instruct
choices	array	
choices[0].index	int	索引
choices[0].finish_reason	string	结束原因 正常结束：stop，token超长截断结束：length
choices[0].delta	object	模型回答
choices[0].refs	array	引用列表，调用自定义模型且模型输出包含文档引用时存在。在流式响应过程中，会实时于存在引用的位置输出引用来源信息，并在finish_reason不为空时输出此次回答包含的所有引用来源信息。
choices[0].refs[0].index	int	引用来源出现顺序
choices[0].refs[0].title	string	引用数据标题
choices[0].refs[0].content	string	引用数据内容
choices[0].refs[0].type	string	引用数据类型，file/qa/web， 分别代表文件知识， Q&A Table和web搜索
choices[0].refs[0].url	string	引用数据url，其中，file和qa数据url访问需配置apikey，web数据url访问无需配置apikey
请求示例
示例如下，请将参数示例值替换为实际值。

纯文本请求示例

curl --location -g --request POST 'https://www.sophnet.com/api/open-apis/v1/chat/completions' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "messages": [
          {
            "role": "system",
            "content": "你是SophNet的智能助手"
        },
        {
            "role": "user",
            "content": "你可以帮我做什么"
        }
    ],
    "model":"Qwen2.5-72B-Instruct"
}'
Python SDK


# 支持兼容OpenAI Python SDK  终端运行：pip install OpenAI
from openai import OpenAI

### 初始化客户端
client = OpenAI(
    api_key= "API_KEY",
    base_url= "https://www.sophnet.com/api/open-apis/v1"
)
### 调用接口
response = client.chat.completions.create(
    model="Qwen2.5-72B-Instruct",
    messages=[
        {"role": "system", "content": "你是SophNet智能助手"},
        {"role": "user", "content": "你可以帮我做些什么"},
    ]
)
# 打印结果
print(response.choices[0].message.content)
Function Call请求示例
HTTP API


curl --location -g --request POST 'https://www.sophnet.com/api/open-apis/v1/chat/completions' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "messages": [
        {
            "role": "user",
            "content": "今日上海天气如何？"
        }
    ],
    "model":"DeepSeek-v3",
    "tools": [
        {
            "type": "function",
            "function":
            {
                "name": "get_weather",
                "description": "Get current temperature for provided coordinates in celsius.",
                "parameters":
                {
                    "type": "object",
                    "properties":
                    {
                        "latitude": {"type": "number"},
                        "longitude": {"type": "number"}
                    },
                    "required": ["latitude", "longitude"],
                    "additionalProperties": false
                },
                "strict": true
            }
        }
    ]
}'

# 请求成功后，从返回值的choices[0].message.tool_calls[0].function获取到函数调用信息
# 其中function.name是函数名，function.arguments中含有函数参数
# 假设已通过函数调用获取到返回值是20，且获取到choices[0].message.tool_calls[0].id = "call_f0j0i4meawn7kqx335d4fsj1"
# 接下来是第二次请求，其中messages列表的第一个与之前相同，第二个为choices[0].message.tool_calls，第三个的构造信息参考如下

curl --location -g --request POST 'https://www.sophnet.com/api/open-apis/v1/chat/completions' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "messages": [
        {
            "role": "user",
            "content": "今日上海天气如何？"
        },
        {
            "content": "",
            "role": "assistant",
            "tool_calls": [
                {
                    "id": "call_f0j0i4meawn7kqx335d4fsj1",
                    "type": "function",
                    "function":
                    {
                        "name": "get_weather",
                        "arguments": "{\"latitude\":31.2304,\"longitude\":121.4737}"
                    }
                }
            ]
        },
        {
            "role": "tool",
            "tool_call_id": "call_f0j0i4meawn7kqx335d4fsj1",
            "content": "20"
        }
    ],
    "model":"DeepSeek-v3",
    "tools": [
        {
            "type": "function",
            "function":
            {
                "name": "get_weather",
                "description": "Get current temperature for provided coordinates in celsius.",
                "parameters":
                {
                    "type": "object",
                    "properties":
                    {
                        "latitude": {"type": "number"},
                        "longitude": {"type": "number"}
                    },
                    "required": ["latitude", "longitude"],
                    "additionalProperties": false
                },
                "strict": true
            }
        }
    ]
}'
多模态请求示例(Qwen VL模型支持多模态参数请求)
HTTP API


curl --location -g --request POST 'https://www.sophnet.com/api/open-apis/projects/v1/chat/completions' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "messages": [{
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "describe the image in 100 words or less"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": "xxx"
                }
            }
        ]
    }],
    "model":"Qwen2.5-VL-72B-Instruct",
    "stream":false
}'
Python SDK


# 支持兼容OpenAI Python SDK  终端运行：pip install OpenAI
from openai import OpenAI

### 初始化客户端
client = OpenAI(
    api_key= "API_KEY",
    base_url= "https://www.sophnet.com/api/open-apis/v1"
)
### 调用接口
response = client.chat.completions.create(
    model="Qwen2.5-VL-72B-Instruct",
    messages=[
        {"role": "system", "content": "你是SophNet智能助手"},
        {
            "role": "user",
            "content": [
                {
                    "type":"text",
                    "text":"描述一下这张图片"
                },
                {
                    "type":"image_url",
                    "image_url":{"url":xxx}
                }]
        },
    ]
)
# 打印结果
print(response.choices[0].message.content)
响应示例
流式 （event-stream）


data:{"object":"chat.completion.chunk","created":1724651635,"model":"Qwen/Qwen2.5-72B-Instruct","choices":[{"index":0,"delta":{"content":"我可以","role":"assistant"},"finish_reason":null}]}

data:{"object":"chat.completion.chunk","created":1724651635,"model":"Qwen/Qwen2.5-72B-Instruct","choices":[{"index":0,"delta":{"content":"提供","role":null},"finish_reason":null}]}

data:{"object":"chat.completion.chunk","created":1724651635,"model":"Qwen/Qwen2.5-72B-Instruct","choices":[{"index":0,"delta":{"content":"智能问答","role":null},"finish_reason":null}]}

data:{"object":"chat.completion.chunk","created":1724651635,"model":"Qwen/Qwen2.5-72B-Instruct","choices":[{"index":0,"delta":{"content":"和帮助。","role":null},"finish_reason":null}]}

data:{"object":"chat.completion.chunk","created":1724651635,"model":"Qwen/Qwen2.5-72B-Instruct","choices":[{"index":0,"delta":{"content":null,"role":null},"finish_reason":"stop"}]}
非流式 (Json)


{
    "object": "chat.completion",
    "created": 1724652804,
    "model": "Qwen/Qwen2.5-72B-Instruct",
    "choices": [
        {
            "index": 0,
            "message": {
                "content": "作为SophNet智能助手，我可以帮助你完成多种任务。如果你有具体的需求或问题，请告诉我！",
                "role": "assistant"
            },
            "finish_reason": "stop"
        }
    ]
}
Function Call (Json) 首次返回


{
    "object":"chat.completion",
    "created":1744967746,
    "model":"DeepSeek-v3",
    "choices":[
        {
            "index":0,
            "message":
            {
                "content":"",
                "role":"assistant",
                "tool_calls":[
                    {
                        "id":"call_f0j0i4meawn7kqx335d4fsj1",
                        "type":"function",
                        "function":
                            {
                                "name":"get_weather",
                                "arguments":"{\"latitude\":31.2304,\"longitude\":121.4737}"
                            }
                    }
                ]
            },
            "finish_reason":"tool_calls"
        }
    ]
}
第二次返回


{
    "object":"chat.completion",
    "created":1744967193,
    "model":"DeepSeek-v3",
    "choices":[
        {
            "index":0,
            "message":
                {
                    "content":"今日上海的天气温度为20°C。",
                    "role":"assistant"
                },
            "finish_reason":"stop",
        }
    ]
}
Speech to text
创建Task
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/speechtotext/transcriptions

请求方式：POST

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
支持两种请求方式

音频文件链接模式Header参数

名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer " + Apikey
音频文件链接模式Body参数
名称	类型	必填	描述
audio_url	string	是	语音url路径
支持音频格式：wav、mp3、m4a、flv、mp4、wma、3gp、amr、aac、ogg-opus、flac
音频限制：音频 URL 时长不能大于5小时，文件大小不超过1GB
识别有效时间：识别结果在服务端保存24小时
easyllm_id	string	是	Easyllm ID
音频文件上传模式Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值multipart/form-data
Authorization	String	是	"Bearer " + Apikey
音频文件链接模式form-data参数
名称	类型	必填	描述
data	JSON字符串	是	与 JSON 模式下的 body 一致，需为字符串形式的 JSON
audio_file	文件	是	本地音频文件
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	描述
task_id	string	任务id
created	int	时间戳
请求示例
HTTP API 音频文件链接模式


curl --location --request POST 'https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/speechtotext/transcriptions' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "audio_url":"YOUR_AUDIO_URL",
    "easyllm_id":"YOUR_SERVICE_ID"
}'
音频文件上传模式


curl --location --request POST 'https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/speechtotext/transcriptions' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: multipart/form-data" \
-F 'data={"easyllm_id":"YOUR_SERVICE_ID"};type=application/json' \
-F "audio_file=@/path/to/your_audio_file.wav;type=audio/wav"
响应示例

{
    "taskId": "10047816884",
    "created": 1724652804
}
查询Task状态、结果
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/projects/{project_id}/easyllms/speechtotext/transcriptions/{taskId}

请求方式：GET

Path参数：
名称	类型	必填	描述
project_id	String	是	项目id
task_id	String	是	语音转文本task_id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
Body参数
无

响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	描述
task_id	string	任务id
status	string	任务状态，waiting：任务等待，doing：任务执行中，success：任务成功，failed：任务失败。
示例值：waiting
result	string	转录结果
errorMsg	string	错误码
请求示例
HTTP API


curl --location --request GET 'https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/speechtotext/transcriptions/{taskId}' \
--header "Authorization: Bearer $API_KEY" \
响应示例

{
  "taskId": "10045132157",
  "status": "success",
  "result": "[0:5.840,0:6.690,0]  哎，张总。\n[0:7.520,0:8.270,1]  还要打吗？\n[0:8.700,0:8.925,0]  哎。\n[0:8.925,0:12.550,1]  我能听到这给你们发了有3张图。\n"
}
流式Speech to text
连接请求说明
基本信息
请求地址：wss://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/stream-speech

请求方式：Websocket

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
Request参数
名称	类型	必填	描述
easyllm_id	String	是	easyllm id
apikey	String	是	Apikey
format	String	是	输入音频格式，支持pcm、wav、mp3、aac
sample_rate	int	是	音频采样率，任意音频采样率，但16k效果更好
heartbeat	bool	是	是否开启心跳，若为false即使发送静音音频也会在60s后超时关闭连接，若为true，发送静音音频将保持连接
音频数据发送请求说明
音频数据发送说明：音频bytes数据，可按照3200的数量发送。
关闭连接请求说明
关闭连接请求说明：发送一个字符串"BYE"来主动关闭连接。
连接响应说明
响应体参数
名称	类型	描述
status	string	连接成功返回'ok'，失败则直接关闭连接
音频识别结果响应说明
响应体参数
名称	类型	描述
text	string	句子级识别结果，当is_sentence_end为false时，包含流式识别的输出结果，而为true则表示最终句子识别结果，下一个消息将是新的句子
begin_time	int	句子开始的时刻，单位为毫秒
end_time	int	句子结束的时刻，单位为毫秒
words	string	字级别的预测结果
is_sentence_end	bool	表示句子是否结束
连接请求示例
Websocket API


const url = `wss://www.sophnet.com/api/open-apis/projects/${ProjectId}/easyllms/stream-speech`
        + `?easyllm_id=${model}`
        + `&apikey=${apikey}`
        + `&format=${format}`
        + `&sample_rate=${sampleRate}`
        + `&heartbeat=true`;

ws = new WebSocket(url);
ws.binaryType = 'arraybuffer';

ws.onopen = () => {
log('WebSocket 已连接: ' + url);
};

ws.onmessage = (evt) => {
if (typeof evt.data === 'string') {
    log('<- ASR_RESULT: ' + evt.data);
} else {
    log('<- binary message (' + evt.data.byteLength + ' bytes)');
}
};

ws.onerror = (err) => {
log('WebSocket 错误: ' + err);
};

ws.onclose = (evt) => {
log(`WebSocket 已关闭: [${evt.code}] ${evt.reason}`);
};
音频数据发送请求示例
Websocket API


ws.send(byteData);
连接响应示例

{"status": "ok"}
音频识别结果响应示例

{"text":"这是深度神经网络的语音","begin_time":660,"end_time":null,"words": ["Word(beginTime=660, endTime=1148, text=这是, punctuation=, fixed=false)", "Word(beginTime=1148, endTime=1636, text=深度, punctuation=, fixed=false)", "Word(beginTime=1636, endTime=2124, text=神经, punctuation=, fixed=false)", "Word(beginTime=2124, endTime=2612, text=网络的, punctuation=, fixed=false)", "Word(beginTime=2612, endTime=3100, text=语音, punctuation=, fixed=false)"], "is_sentence_end": false}

{"text":"这是深度神经网络的语音识别","begin_time":660,"end_time":null,"words": ["Word(beginTime=660, endTime=1148, text=这是, punctuation=, fixed=false)", "Word(beginTime=1148, endTime=1636, text=深度, punctuation=, fixed=false)", "Word(beginTime=1636, endTime=2124, text=神经, punctuation=, fixed=false)", "Word(beginTime=2124, endTime=2612, text=网络的, punctuation=, fixed=false)", "Word(beginTime=2612, endTime=3100, text=语音, punctuation=, fixed=false)", "Word(beginTime=3100, endTime=3500, text=识别, punctuation=, fixed=false)"], "is_sentence_end": false}

{"text":"这是深度神经网络的语音识别","begin_time":660,"end_time":null,"words": ["Word(beginTime=660, endTime=1148, text=这是, punctuation=, fixed=false)", "Word(beginTime=1148, endTime=1636, text=深度, punctuation=, fixed=false)", "Word(beginTime=1636, endTime=2124, text=神经, punctuation=, fixed=false)", "Word(beginTime=2124, endTime=2612, text=网络的, punctuation=, fixed=false)", "Word(beginTime=2612, endTime=3100, text=语音, punctuation=, fixed=false)", "Word(beginTime=3100, endTime=3588, text=识别, punctuation=, fixed=false)"], "is_sentence_end": false}

{"text":"这是深度神经网络的语音识别模型。","begin_time":660,"end_time":5540,"words": ["Word(beginTime=660, endTime=1148, text=这是, punctuation=, fixed=false)", "Word(beginTime=1148, endTime=1636, text=深度, punctuation=, fixed=false)", "Word(beginTime=1636, endTime=2124, text=神经, punctuation=, fixed=false)", "Word(beginTime=2124, endTime=2612, text=网络的, punctuation=, fixed=false)", "Word(beginTime=2612, endTime=3100, text=语音, punctuation=, fixed=false)", "Word(beginTime=3100, endTime=3588, text=识别, punctuation=, fixed=false)", "Word(beginTime=3588, endTime=4076, text=模型, punctuation=, fixed=false)"], "is_sentence_end": true}

{"text":"请","begin_time":6001,"end_time":null,"words": ["Word(beginTime=6001, endTime=6502, text=请, punctuation=, fixed=false)"], "is_sentence_end": false}
Embeddings
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/embeddings

请求方式：POST

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
Body参数
名称	类型	必填	描述
input_texts	array(string)	是	数组中每一个元素是一个文本，最大支持10个文本，每个文本最大8192个Tokens
dimensions	integer	是	输出Embeddings的维度，支持1,024/768/512/256/128/64
easyllm_id	string	是	Easyllm ID
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	描述
id	string	任务id
usage	dict	模型推理时Token使用情况
data	array	模型推理结果
请求示例
HTTP API


curl --location --request POST 'https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/embeddings' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "easyllm_id": "{YOUR_EASYLLM_ID}",
    "input_texts": ["你好", "很高兴认识你"],
    "dimensions": 1024
}'
响应示例

{
    "id": "",
    "object": "list",
    "usage": {
        "prompt_tokens": 4,
        "completion_tokens": null,
        "total_tokens": 4,
        "prompt_tokens_details": null,
        "completion_tokens_details": null
    },
    "data": [
        {
            "embedding": [
                -0.08296291530132294,
                0.03833295777440071,
                ...
            ],
            "index": 0,
            "object": "embedding"
        },
        {
            "embedding": [
                -0.05998880788683891,
                0.04025664180517197,
                ...
            ],
            "index": 1,
            "object": "embedding"
        }
    ]
}
Document Parse
请求说明
基本信息
功能描述：高效转换主流格式文档至精准、易用的Markdown文本内容。上传文件（form-data），输出文档内容（Markdown）

请求地址：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/doc-parse

请求方式：POST

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值multipart/form-data
Authorization	String	是	"Bearer " + Apikey
form-data参数
名称	类型	必填	描述
file	file	是	文档，支持pdf,docx,doc,xlsx,xls,txt,ppt,pptx格式，大小<50MB
easyllm_id	string	是	Easyllm ID
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
data	string	文档解析结果（Markdown）
请求示例
HTTP API


curl --location --request POST 'https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/doc-parse' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: multipart/form-data" \
--form 'file=@"YOUR_DOCUMENT"' \
--form 'easyllm_id="YOUR_SERVICE_ID"'
Python SDK


# 使用SDK前需要安装python库  终端运行：pip install fastmodels-kit
from fastmodels import Client

# 初始化客户端
client = Client(
    api_key= "YOUR_API_KEY",
    project_id= "YOUR_PROJECT_ID"
)
# 调用接口
response = client.easyllm.doc_parse.create(
    easyllm_id="YOUR_SERVICE_ID",
    file_path="YOUR_FILE_PATH"
)
# 打印输出
print(response.data)
响应示例

{
  "data": "文件编号：HR-2023-06-3-1\n\n发布单位：AMT\n\n发布对象：全员\n\n发布日期：2023.11.16\n\n生效日期：2023.11.16\n\n**管理制度**\n\n......"
}
Text to voice
请求说明
基本信息
功能描述：文字转语音服务。发送文件，输出语音（默认为mp3格式）。

请求地址：

流式接口：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/voice/synthesize-audio-stream

非流式接口：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/voice/synthesize-audio

请求方式：POST

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
Body参数
名称	类型	必填	描述
text	array(string)	是	需要转语音的字符串列表
synthesis_param	object	是	转语音参数
synthesis_param.model	string	是	指定模型，默认值为"cosyvoice-v1"
synthesis_param.voice	string	否	指定音色，默认值为"longxiaochun"
synthesis_param.format	string	否	指定音频编码格式及采样率，格式为"文件格式_采样率_通道_比特率"，例如MP3_16000HZ_MONO_128KBPS代表音频格式为mp3，采样率为16kHz。若未指定format，系统将根据voice参数自动选择该音色的推荐格式。
synthesis_param.volume	number	否	指定音量，默认值为50，取值范围：[0-100]
synthesis_param.speechRate	number	否	指定语速，默认值为1，输入范围：[0.5,2]
synthesis_param.pitchRate	number	否	指定语调，默认值为1，取值范围：[0.5,2]
easyllm_id	string	是	Easyllm ID
响应说明
流式接口
响应头参数
名称	值	描述
Content-Type	text/event-stream	
响应体参数
名称	类型	值
data	string	base64编码的语音数据
非流式接口
响应头参数
名称	值	描述
Content-Type	audio/mpeg, audio/wav, audio/L16, application/octet-stream	二进制音频流，依据具体格式返回对应类型
请求示例
HTTP API


curl --location --request POST 'https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/voice/synthesize-audio-stream' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "easyllm_id": "YOUR_SERVICE_ID",
    "text": ["这是要合成的文本", "第二段文本"],
    "synthesis_param": {
        "model": "cosyvoice-v1",
        "voice": "longxiaochun",
        "format": "WAV_16000HZ_MONO_16BIT",
        "volume": 80,
        "speechRate": 1.2,
        "pitchRate": 1.0
    }
}'
Python requests

NOTE: 这里演示如何输出为音频文件
流式接口


import requests
import json
import base64

projectId = "YOUR_PROJECT_ID"
easyllmId = "YOUR_EASYLLM_SERVICE_ID"
API_KEY = "YOUR_API_KEY"

url = f"https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/voice/synthesize-audio-stream"

headers = {
   'Content-Type': 'application/json',
   'Authorization': 'Bearer ' + API_KEY,
}

payload = json.dumps({
   "easyllm_id": easyllmId,
   "text": [
       "测试",
   ],
   "synthesis_param": {
       "model": "cosyvoice-v1",
       "voice": "longxiaochun",
       "format": "MP3_16000HZ_MONO_128KBPS",
       "volume": 80,
       "speechRate": 1.2,
       "pitchRate": 1
   }
})

response = requests.request("POST", url, headers=headers, data=payload)
for chunk in response.iter_lines(decode_unicode=True):
    with open("output.mp3","ab") as f:
        if chunk:
            if (frame:=json.loads(chunk[5:])["audioFrame"]):
                f.write(base64.b64decode(frame))
非流式接口


import requests
import json
import base64

projectId = "YOUR_PROJECT_ID"
easyllmId = "YOUR_EASYLLM_SERVICE_ID"
API_KEY = "YOUR_API_KEY"

url = f"https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/voice/synthesize-audio"

headers = {
   'Content-Type': 'application/json',
   'Authorization': 'Bearer ' + API_KEY,
}

payload = json.dumps({
   "easyllm_id": easyllmId,
   "text": [
       "测试",
   ],
   "synthesis_param": {
       "model": "cosyvoice-v1",
       "voice": "longxiaochun",
       "format": "MP3_16000HZ_MONO_128KBPS",
       "volume": 80,
       "speechRate": 1.2,
       "pitchRate": 1
   }
})

response = requests.request("POST", url, headers=headers, data=payload)
with open("output.mp3","wb") as f:
    f.write(response.content)
响应示例

{'status': 'accepting', 'usage': None, 'audioFrame': '{BASE64 encoded data}'}
Text to image
创建Task
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/texttoimage/task

请求方式：POST

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer " + Apikey
Body参数
名称	类型	必填	描述
easyllm_id	string	是	Easyllm ID
model	string	是	模型，当前支持flux-schnell, flux-dev
input.prompt	string	是	提示词
parameters.size	string	否	生成图像的分辨率，目前支持"5121024, 768512, 7681024, 1024576, 5761024, 10241024"六种分辨率，默认为1024*1024像素。
parameters.seed	int	否	图片生成时候的种子值，如果不提供，则算法自动用一个随机生成的数字作为种子。
parameters.steps	int	否	图片生成的推理步数，如果不提供，则默认为30。 flux-schnell 模型官方默认 steps 为4，flux-dev 模型官方默认 steps 为50。
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	描述
output.taskId	string	任务id
output.taskStatus	string	任务状态，包括SUCCEEDED, FAILED, CANCELED, PENDING, SUSPENDED, RUNNING
获取task结果
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/texttoimage/task/{TaskId}

请求方式：GET

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
TaskId	String	是	任务id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer " + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	描述
output.taskId	string	任务id
output.taskStatus	string	任务状态，包括SUCCESSED, FAILED, CANCELED, PENDING, SUSPENDED, RUNNING
output.results[0].url	string	生成的图片链接
请求示例
HTTP API


#!/bin/bash
# 请配置变量ProjectId和EasyllmId以及API_KEY

# Step 1: 发起异步请求
response=$(curl --silent --request POST 'https://www.sophnet.com/api/open-apis/projects/${ProjectId}/easyllms/texttoimage/task' \
  --header "Authorization: Bearer ${API_KEY}" \
  --header 'Content-Type: application/json' \
  --data '{
    "easyllm_id": "${EasyllmId}",
    "model": "flux-dev",
    "input": {
        "prompt": "奔跑的小猫"
    },
    "parameters": {
        "size": "1024*1024",
        "seed": 42,
        "steps": 4
    }
}')

echo
echo $response
echo

taskId=$(echo "$response" | grep -oP '(?<="taskId":")[^"]+')

if [ -z "$taskId" ]; then
  echo "任务提交失败，未获取到 taskId"
  exit 1
fi

echo "任务已提交，taskId: $taskId"

# Step 2: 轮询任务状态，直到完成
status="PENDING"
while [ "$status" = "PENDING" ] || [ "$status" = "RUNNING" ]; do
  sleep 2
  poll_response=$(curl --silent --location "https://www.sophnet.com/api/open-apis/projects/${ProjectId}/easyllms/texttoimage/task/${taskId}" \
    --header "Authorization: Bearer ${API_KEY}")

  status=$(echo "$poll_response" | grep -oP '(?<="taskStatus":")[^"]+')
  echo "当前状态: $status"
done

echo
echo $poll_response
echo

# Step 3: 成功后下载图片
if [ "$status" = "SUCCEEDED" ]; then
  image_url=$(echo "$poll_response" | grep -oP '(?<="url":")[^"]+')
  if [ -n "$image_url" ]; then
    echo "下载图片: $image_url"
    curl --silent "$image_url" -o "${taskId}.png"
    echo "图片已保存为 ${taskId}.png"
  else
    echo "未找到图片 URL"
  fi
else
  echo "任务失败，状态为: $status"
  echo "完整响应: $poll_response"
fi
Video generator
创建Task
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/videogenerator/task

请求方式：POST

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer " + Apikey
Body参数
名称	类型	必填	描述
easyllm_id	string	是	Easyllm ID
model	string	是	模型，当前支持Wan2.1-T2V-14B, Wan2.1-I2V-14B
content	array	是	内容列表
content[0].type	string	是	消息类型，包括text和image_url
content[0].text	string	是	type=text时的文本提示词
content[0].image_url.url	string	否	type=image_url时必填，表示图片，可以是形如"data:image/jpeg;base64,{BASE64_IMG}"的图片编码，或者可公网访问的图片url
content[0].role	string	否	图片的位置，first_frame表示首帧图片，当前支持的模型仅支持首帧
parameters.resolution	string	否	视频分辨率，支持480p, 720p，默认为720p
parameters.ratio	string	否	视频宽高比，支持16:9或9:16
parameters.duration	string	否	视频时长，当前所有模型仅支持5s
parameters.fps	string	否	视频帧率，当前所有模型仅支持16
parameters.watermark	string	否	是否包含AI生成的水印，可选true或false，默认是false
parameters.seed	string	否	种子值（整数字符串），用于控制生成内容的随机性取值范围：[-1, 2^32-1]之间的整数。默认值-1， -1表示随机
Note: 传入的图片需满足以下条件
图片格式：jpeg、png、webp、bmp、tiff、gif。
宽高比（宽/高）：在范围 (0.4, 2.5) 。
宽高长度（px）：(300, 6000)。
大小：小于30MB。
响应说明
响应头参数
名称	值	描述
Content-Type	text/plain	直接返回taskId
响应体参数
名称	类型	描述
taskId	string	任务id
获取task结果
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/videogenerator/task/{TaskId}

请求方式：GET

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
TaskId	String	是	任务id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer " + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	描述
status	string	任务状态，包括queued, running, cancelled, succeeded, failed
content.video_url	string	生成的视频链接
usage.completion_tokens	int	此次任务消耗的tokens
请求示例
HTTP API


#!/bin/bash
# 请配置变量ProjectId和EasyllmId以及API_KEY

# Step 1: 提交任务
response=$(curl -s -X POST 'https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/videogenerator/task' \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_KEY}" \
  -d '{
    "easyllm_id": "${EasyllmId}",
    "model": "Wan2.1-T2V-14B",
    "content": [
        {
            "type": "text",
            "text": "小羊在草原上散步"
        }
    ],
    "parameters": {
        "ratio": "16:9",
        "duration": 5,
        "fps": 16
    }
}')

echo
echo $response
echo

taskId=$response

if [ -z "$taskId" ]; then
  echo "任务提交失败，未获取到任务 ID"
  exit 1
fi

echo "任务已提交，ID: $taskId"

# Step 2: 轮询状态
status="queued"
while [[ "$status" == "queued" || "$status" == "running" ]]; do
  sleep 2
  poll_response=$(curl -s -X GET "https://www.sophnet.com/api/open-apis/projects/${ProjectId}/easyllms/videogenerator/task/${taskId}" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${API_KEY}")

  status=$(echo "$poll_response" | grep -oP '(?<="status":")[^"]+')
  echo "当前状态: $status"
done

echo
echo $poll_response
echo

# Step 3: 下载视频
if [ "$status" == "succeeded" ]; then
  video_url=$(echo "$poll_response" | grep -oP '(?<="video_url":")[^"]+')
  if [ -n "$video_url" ]; then
    echo "下载视频: $video_url"
    curl -s "$video_url" -o "${taskId}.mp4"
    echo "视频已保存为 ${taskId}.mp4"
  else
    echo "任务成功但未获取到视频链接"
  fi
else
  echo "任务失败，状态: $status"
  echo "完整响应: $poll_response"
fi
Image OCR
请求说明
基本信息
功能描述：图片OCR服务。发送图片，输出图片中的文本信息。

请求地址：https://www.sophnet.com/api/open-apis/projects/{ProjectId}/easyllms/image-ocr

请求方式：POST

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
Body参数
名称	类型	必填	描述
type	string	是	图片类型，固定为"image_url"
image_url	object	是	图片参数
image_url.url	string	是	图片，可以是base64图片，固定格式为"data:image/jpeg;base64,{base64_data}"；也可以是图片url链接
use_doc_ori	int	是	是否使用文档方向分类，1表示使用，0表示不使用
use_table	int	是	是否使用表格识别，1表示使用，0表示不使用；此项为1时use_html_out必须为1
use_html_out	int	是	是否使用html输出，1表示使用，0表示不使用；使用表格识别时，此项必须为1
easyllm_id	string	是	Easyllm ID
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	int	0表示成功,其他值表示失败
message	string	调用成功返回请求成功,否则返回错误信息
result	array(object)	返回的结果,有一个个的段落组成,如果是use_html_out我1,则list的长度为1，有每个段落包含以下字段
result[0].label	string	段落的类型,可以是text，table，html等
result[0].texts	string	段落的文本
result[0].position	array(int)	段落的位置，格式为left,top,right,bottom
请求示例
HTTP API


curl --location --request POST 'https://www.sophnet.com/api/open-apis/projects/{projectId}/easyllms/image-ocr' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "easyllm_id": "YOUR_SERVICE_ID",
    "type":"image_url",
    "image_url": {
            "url": "data:image/jpeg;base64,/9j/..."
    },
    "use_doc_ori": 1,
    "use_table":1,
    "use_html_out":1
}'
响应示例

{
    "status":0,
    "message":"请求成功",
    "result": [
        {
            "label": "text",
            "texts": "测试",
            "position": "0,0,720,1920"
        }
    ]
}
Chat completions + Text to voice
请求说明
基本信息
请求地址：https://www.sophnet.com/api/open-apis/v1/chat/completions-with-voice-output

请求方式：POST

Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
Body参数
名称	类型	必填	描述
chat_completion_req	dict	是	包含Chat completions的参数
speech_synthesis_req	dict	是	包含Text to voice的部分参数
chat_completion_req参数：参考Chat completions章节，仅支持流式

speech_synthesis_req参数：仅支持流式

名称	类型	必填	描述
synthesis_param	dict	否	转语音参数
synthesis_param.model	string	否	指定模型，默认值为"cosyvoice-v1"
synthesis_param.voice	string	否	指定音色，默认值为"longxiaochun"，支持longwan/longcheng/longhua/longxiaochun/longxiaoxia/longxiaocheng/longxiaobai/longlaotie/longshu/longshuo/longjing/longyue/loongstella/loongbella
synthesis_param.format	string	否	指定音频编码格式及采样率，格式为"文件格式_采样率_通道_比特率"，例如MP3_16000HZ_MONO_128KBPS代表音频格式为mp3，采样率为16kHz。若未指定format，系统将根据voice参数自动选择该音色的推荐格式。
synthesis_param.volume	number	否	指定音量，默认值为50，取值范围：[0-100]
synthesis_param.speechRate	number	否	指定语速，默认值为1，输入范围：[0.5,2]
synthesis_param.pitchRate	number	否	指定语调，默认值为1，取值范围：[0.5,2]
easyllm_id	string	是	Easyllm ID
响应说明
基本信息：会返回两类消息，分别是Chat completions响应消息和Text to voice响应消息

Chat completions响应说明：参考Chat completions章节

Text to voice响应说明

名称	类型	值
audioFrame	dict	语音数据结果
audioFrame.array	String	Base64编码的音频数据
status	String	目前服务状态
请求示例
示例如下，请将参数示例值替换为实际值。

curl请求示例

curl --location --request POST 'https://www.sophnet.com/api/open-apis/v1/chat/completions-with-voice-output' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer $API_KEY' \
--data-raw '{
    "chat_completion_req": {
    "messages": [
        {
            "role": "user",
            "content": "你好"
        }
    ],
    "model":"Qwen2.5-32B-Instruct",
    "stream": true
    },
    "speech_synthesis_req": {
        "easyllm_id": "${easyllm_id}"
    }
}'
响应示例

{"choices": [{"delta": {"content": "", "role": "assistant"},"index": 0}],"created": 1749037853,"id": "chatcmpl-xxx","model": "Qwen2.5-32B-Instruct","object": "chat.completion.chunk"}

{"choices":[{"delta":{"content":"你好"},"index":0}],"created":1749037853,"id":"chatcmpl-xxx","model":"Qwen2.5-32B-Instruct","object":"chat.completion.chunk"}

{"choices":[{"delta":{"content":"！"},"index":0}],"created":1749037853,"id":"chatcmpl-xxx","model":"Qwen2.5-32B-Instruct","object":"chat.completion.chunk"}

{"choices":[{"delta":{"content":"有什么"},"index":0}],"created":1749037853,"id":"chatcmpl-xxx","model":"Qwen2.5-32B-Instruct","object":"chat.completion.chunk"}

{"status":"accepting","usage":null,"audioFrame":"SUQzBAAA..."}

{"choices":[{"delta":{"content":"可以帮助你的吗？"},"index":0}],"created":1749037853,"id":"chatcmpl-xxx","model":"Qwen2.5-32B-Instruct","object":"chat.completion.chunk"}

{"status":"accepting","usage":null,"audioFrame":null}

{"status":"accepting","usage":null,"audioFrame":"//PCxO1..."}

{"choices":[{"delta":{"content":""},"finish_reason":"stop","index":0}],"created":1749037853,"id":"chatcmpl-xxx","model":"Qwen2.5-32B-Instruct","object":"chat.completion.chunk"}

{"status":"accepting","usage":null,"audioFrame":"//PAxPJh..."}

{"status":"finish","usage":{"characters":26},"audioFrame":null}

...
语音对话
上行事件
连接请求事件说明
基本信息
请求地址：wss://www.sophnet.com/api/open-apis/projects/{ProjectId}/chat/speech-chat

请求方式：Websocket

Path参数：
名称	类型	必填	描述
ProjectId	String	是	项目id
Request参数
名称	类型	必填	描述
token	String	是	包含“Bearer ”前缀，后跟Apikey
model	String	是	Chat completions服务的模型名
asr_easyllm_id	String	是	Speech to text服务的easyllm id
tts_easyllm_id	String	是	Text to voice服务的easyllm id
对话配置更新事件说明
事件类型：chat.update

事件说明：该事件发生在获得连接请求响应后，可选地更新对话配置，可更新多次，若不更新则使用默认参数。仅支持流式

参数说明：字段create_transcription_task_req、chat_completion_with_voice_output_req.chat_completion_req和chat_completion_with_voice_output_req.speech_synthesis_req必须设置，其内容为空则所有字段使用默认参数，若部分字段为空则未设置的字段将使用默认值。

默认参数：


{
    "create_transcription_task_req": {
        "heartbeat": false,
        "speech_recognition_param": {
            "sample_rate": 16000,
            "format": "wav"
        }
    },
    "chat_completion_with_voice_output_req": {
        "chat_completion_req": {
            "messages": [],
            "model": "${model参数}",
            "stream": true
        },
        "speech_synthesis_req": {
            "stream": true,
            "synthesis_param": {
                "model": "cosyvoice-v1",
                "voice": "longxiaochun",
                "format": "MP3_22050HZ_MONO_256KBPS"
            }
        }
    },
    "asr_mode": "online"
}
事件消息结构：
参数	类型	必填	说明
event_type	String	是	事件类型，固定为chat.update
message	String	是	配置参数JSON字符串，格式{"create_transcription_task_req": {可参考流式Speech to text中Request参数说明}, "chat_completion_with_voice_output_req": {可参考Chat completions + Text to voice中Body参数说明}, "asr_mode": asr加载模式}
message.asr_mode	String	否	目前支持两种："online"/"refresh"，默认为"online"模式。"refresh"模式指每次执行llm+tts时清空asr音频缓存，对于输入截断的音频建议开启，"online"模式指总是开启ASR，对于连续音频流建议开启。
流式上传音频片段
事件说明：该事件发生后对话配置不允许再被更新，并将根据heartbeat设置的参数判断超时，超时将关闭连接。

事件消息结构：二进制音频数据块，可按照100ms、200ms传输，根据实际情况调整。

音频提交事件说明
事件类型：input_audio_buffer.complete

事件说明：该事件发生后将停止ASR，并将ASR识别结果作为LLM输入，转为LLM+TTS推理。

事件消息结构：

参数	类型	必填	说明
event_type	String	是	事件类型，固定为input_audio_buffer.complete
音频缓存清理事件说明
事件类型：input_audio_buffer.clear

事件说明：该事件发生后将清空ASR服务的音频缓存bytes数据（可能会发送一条ASR识别结果），并清空已识别结果。若在未发送过音频bytes数据情况下执行该事件，将报错。

事件消息结构：

参数	类型	必填	说明
event_type	String	是	事件类型，固定为input_audio_buffer.clear
上下文清理事件说明
事件类型：conversation.clear

事件说明：该事件发生后将清理之前的LLM上下文记录，但对话配置更新事件设置的上下文不会被清理。

事件消息结构：

参数	类型	必填	说明
event_type	String	是	事件类型，固定为conversation.clear
打断事件说明
事件类型：conversation.chat.cancel

事件说明：该事件发生后将中断LLM+TTS推理，并转为ASR。

事件消息结构：

参数	类型	必填	说明
event_type	String	是	事件类型，固定为conversation.chat.cancel
心跳事件说明
事件类型：ping

事件说明：需要定时发送该消息，如果超过60s，将关闭连接。

事件消息结构：

参数	类型	必填	说明
event_type	String	是	事件类型，固定为ping
下行事件
连接请求事件响应
事件类型：chat.created

事件说明：需要等连接响应返回后才能执行其他事件。

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为chat.created
status	int	事件状态，0表示成功，非0表示失败
message	String	失败原因说明
对话配置更新事件响应
事件类型：chat.update

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为chat.update
status	int	事件状态，0表示成功，非0表示失败
message	String	失败原因说明
增量音频识别结果响应
事件说明：流式返回音频识别结果。

响应体参数：参考流式Speech to text中返回响应

音频提交事件响应
事件类型：input_audio_buffer.completed

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为input_audio_buffer.completed
status	int	事件状态，0表示成功，非0表示失败
message	String	失败原因说明
音频缓存清空事件响应
事件类型：input_audio_buffer.cleared

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为input_audio_buffer.cleared
status	int	事件状态，0表示成功，非0表示失败
message	String	失败原因说明
上下文清理事件响应
事件类型：conversation.cleared

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为conversation.cleared
status	int	事件状态，0表示成功，非0表示失败
message	String	失败原因说明
打断事件响应
事件类型：conversation.chat.canceled

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为conversation.chat.canceled
status	int	事件状态，0表示成功，非0表示失败
message	String	失败原因说明
增量LLM+TTS推理结果响应
事件说明：流式返回LLM和TTS推理结果。

响应体参数：参考Chat completions + Text to voice中返回响应

当次对话完成响应
事件类型：conversation.chat.completed

事件说明：在LLM+TTS正常推理结束、发生错误或被打断，则该消息会被返回，将开启ASR。

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为conversation.chat.completed
status	int	固定为0
message	String	固定为空
心跳事件响应
事件类型：pong

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为pong
status	int	事件状态，0表示成功，非0表示失败
message	String	失败原因说明
错误响应
事件类型：error

事件说明：其他错误，例如请求参数或运行时错误。

响应体参数

参数	类型	说明
event_type	String	事件类型，固定为error
status	int	非0
message	String	错误原因说明
请求/响应示例
连接请求示例
连接请求

const url = `wss://www.sophnet.com/api/open-apis/projects/{ProjectId}/chat/speech-chat`
              + `?model=${model}`
              + `&token=Bearer ${apikey}`
              + `&asr_easyllm_id=${asr_easyllm_id}`
              + `&tts_easyllm_id=${tts_easyllm_id}`;

ws = new WebSocket(url);

ws.onopen = () => {
    log('WebSocket 已连接: ' + url);
};

ws.onmessage = (evt) => {
    log('<- RESULT: ' + evt.data);
};

ws.onerror = (err) => {
log('WebSocket 错误: ' + err);
};

ws.onclose = (evt) => {
log(`WebSocket 已关闭: [${evt.code}] ${evt.reason}`);
};
连接响应

{"status":0,"message":"","event_type":"chat.created"}
对话配置更新示例
对话配置更新请求

ws.send('{"event_type":"chat.update","message":"{\\"create_transcription_task_req\\":{\\"service_uuid\\":\\"\\"},\\"chat_completion_with_voice_output_req\\":{\\"chat_completion_req\\":{\\"messages\\":[{\\"role\\":\\"system\\",\\"content\\":\\"你是人工智能助手。\\"}],\\"model\\":\\"\\",\\"stream\\":true},\\"speech_synthesis_req\\":{\\"service_id\\":\\"\\"}}}"}');
对话配置更新响应

{"status":0,"message":"","event_type":"chat.updated"}
流式上传音频片段示例
Websocket API


ws.send(byteData);
ASR结果、LLM结果、TTS结果响应示例
可分别参考流式Speech to text和Chat completions + Text to voice中的返回示例。

自定义模型-文件知识
上传文件
请求说明
基本信息
功能描述：为服务新增文件知识，此接口为自动注入，无需再次注入我的模型

请求地址：https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/files

请求方式：POST

Path参数：
名称	类型	必填	描述
serviceId	String	是	服务id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值multipart/form-data
Authorization	String	是	"Bearer " + Apikey
form-data参数
名称	类型	必填	描述
file	file	是	文档，支持pdf,docx,xlsx,txt,pptx格式，大小<30MB
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	string	请求状态码，0为成功
message	string	请求执行状态
result	object	执行结果
result.id	string	文档id
result.title	string	文档标题
result.snippet	string	文档内容片段
result.status	string	文档状态，ENABLED为已启用，NOT_ENABLED为未启用
请求示例
HTTP API


curl --location --request POST 'https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/files' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: multipart/form-data" \
--form 'file=@"YOUR_DOCUMENT"' \
--form 'easyllm_id="YOUR_SERVICE_ID"'
响应示例

{
    "status": 0,
    "message": "请求成功",
    "result": {
        "id": "4pw0y0Foa0lNubkPUriLkA",
        "title": "你的文档标题",
        "snippet": "文档内容片段",
        "status": "ENABLED",
        "createdAt": "2025-03-17T06:21:09.175+00:00"
    },
    "timestamp": 1742192469480
}
查询服务已绑定文件列表
请求说明
基本信息
功能描述：查询服务已绑定文件列表

请求地址：https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/files

请求方式：GET

Path参数：
名称	类型	必填	描述
serviceId	String	是	服务id
Query参数：
名称	类型	必填	描述
pageNum	Number	否	分页页数
pageSize	Number	否	分页页大小
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	string	请求状态码，0为成功
message	string	请求执行状态
result	object	执行结果
result.pageNum	number	当前页码
result.pageSize	number	当前页面尺寸
result.size	number	当前页面item数量
result.pages	number	当总页数
result.total	number	总item数量
result.list	List<object>	文档列表
result.list[0].id	string	文档id
result.list[0].title	string	文档标题
result.list[0].snippet	string	文档内容片段
result.list[0].status	string	文档状态，ENABLED为已启用，NOT_ENABLED为未启用
请求示例
HTTP API


curl --location --request GET 'https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/files?pageNum=1&&pageSize=10' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
响应示例

{
    "status": 0,
    "message": "请求成功",
    "result": {
        "pageNum": 1,
        "pageSize": 10,
        "size": 1,
        "pages": 1,
        "total": 1,
        "list": [
            {
                "id": "4pw0y0Foa0lNubkPUriLkA",
                "title": "你的文档标题",
                "snippet": "文档内容片段",
                "status": "ENABLED",
                "createdAt": "2025-03-17T06:21:09.175+00:00"
            }
        ]
    },
    "timestamp": 1740536970407
}
获取文件详情
请求说明
基本信息
功能描述：查询文件详情

请求地址：https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/files/{fileId}

请求方式：GET

Path参数：
名称	类型	必填	描述
serviceId	String	是	服务id
fileId	String	是	文件id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	string	请求状态码，0为成功
message	string	请求执行状态
result	object	执行结果
result.id	string	文档id
result.title	string	文档标题
result.content	string	文档内容
result.downloadUrl	string	文档源文件下载地址
请求示例
HTTP API


curl --location --request GET 'https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/files/{fileId}' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
响应示例

{
    "status": 0,
    "message": "请求成功",
    "result": {
        "id": "4pw0y0Foa0lNubkPUriLkA",
        "title": "你的文档标题",
        "content": "文档内容",
        "downloadUrl": "下载地址",
        "createdAt": "2025-03-17T06:21:09.175+00:00"
    },
    "timestamp": 1742192469480
}
删除文件
请求说明
基本信息
功能描述：删除服务中的指定文件

请求地址：https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/files/{fileId}

请求方式：DELETE

Path参数：
名称	类型	必填	描述
serviceId	String	是	服务id
fileId	String	是	文件id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	string	请求状态码，0为成功
message	string	请求执行状态
result	object	执行结果
请求示例
HTTP API


curl --location --request DELETE 'https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/files/{fileId}' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
响应示例

{
    "status": 0,
    "message": "请求成功",
    "timestamp": 1742192469480
}
自定义模型-Q&A Table
新增QA记录
请求说明
基本信息
功能描述：为服务新增QA记录

请求地址：https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/qa-records

请求方式：POST

Path参数：
名称	类型	必填	描述
serviceId	String	是	服务id
Body参数：
名称	类型	必填	描述
question	String	是	QA问题，最长为200个字符
answer	String	是	QA答案，最长为1000个字符
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	string	请求状态码，0为成功
message	string	请求执行状态
result	object	执行结果
result.id	string	QA记录id
result.question	string	QA记录问题
result.answer	string	QA记录答案
请求示例
HTTP API


curl --location --request POST 'https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/qa-records' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "question": "你是谁？",
    "answer": "我是SophNet智能助手。"
}'
响应示例

{
    "status": 0,
    "message": "请求成功",
    "result": {
        "id": "79CNI1obN033Wi8mydOrZC",
        "question": "你是谁？",
        "answer": "我是SophNet智能助手。",
        "createdAt": "2025-03-14T06:23:42.194+00:00"
    },
    "timestamp": 1742192469480
}
修改QA记录
请求说明
基本信息
功能描述：修改服务QA记录

请求地址：https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/qa-records/{qaId}

请求方式：POST

Path参数：
名称	类型	必填	描述
serviceId	String	是	服务id
qaId	String	是	QA记录id
Body参数：
名称	类型	必填	描述
question	String	是	QA问题，最长为200个字符
answer	String	是	QA答案，最长为1000个字符
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	string	请求状态码，0为成功
message	string	请求执行状态
result	object	执行结果
result.id	string	QA记录id
result.question	string	QA记录问题
result.answer	string	QA记录答案
请求示例
HTTP API


curl --location --request POST 'https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/qa-records/{qaId}' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \ 
--data-raw '{
    "question": "你是谁？",
    "answer": "我是SophNet智能助手。"
}'
响应示例

{
    "status": 0,
    "message": "请求成功",
    "result": {
        "id": "79CNI1obN033Wi8mydOrZC",
        "question": "你是谁？",
        "answer": "我是SophNet智能助手。",
        "createdAt": "2025-03-14T06:23:42.194+00:00"
    },
    "timestamp": 1742192469480
}
查询服务已绑定QA记录列表
请求说明
基本信息
功能描述：查询服务已绑定QA记录列表

请求地址：https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/qa-records

请求方式：GET

Path参数：
名称	类型	必填	描述
serviceId	String	是	服务id
Query参数：
名称	类型	必填	描述
pageNum	Number	否	分页页数
pageSize	Number	否	分页页大小
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	string	请求状态码，0为成功
message	string	请求执行状态
result	object	执行结果
result.pageNum	number	当前页码
result.pageSize	number	当前页面尺寸
result.size	number	当前页面item数量
result.pages	number	当总页数
result.total	number	总item数量
result.list	List<object>	QA记录文档列表
result.list[0].id	string	QA记录文档id
result.list[0].question	string	QA记录文档问题
result.list[0].answer	string	QA记录文档答案
请求示例
HTTP API


curl --location --request GET 'https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/qa-records?pageNum=1&&pageSize=10' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
响应示例

{
    "status": 0,
    "message": "请求成功",
    "result": {
        "pageNum": 1,
        "pageSize": 10,
        "size": 1,
        "pages": 1,
        "total": 1,
        "list": [
            {
                "id": "79CNI1obN033Wi8mydOrZC",
                "question": "你是谁？",
                "answer": "我是SophNet智能助手。",
                "createdAt": "2025-03-14T06:23:42.194+00:00"
            }
        ]
    },
    "timestamp": 1740536970407
}
删除QA记录
请求说明
基本信息
功能描述：删除服务中的指定QA记录

请求地址：https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/qa-records/{qaId}

请求方式：DELETE

Path参数：
名称	类型	必填	描述
serviceId	String	是	服务id
qaId	String	是	QA记录id
Header参数
名称	类型	必填	描述
Content-Type	String	是	固定值application/json
Authorization	String	是	"Bearer" + Apikey
响应说明
响应头参数
名称	值	描述
Content-Type	application/json	
响应体参数
名称	类型	值
status	string	请求状态码，0为成功
message	string	请求执行状态
result	object	执行结果
请求示例
HTTP API


curl --location --request DELETE 'https://www.sophnet.com/api/open-apis/service/{serviceId}/kn/qa-records/{qaId}' \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json"
响应示例

{
    "status": 0,
    "message": "请求成功",
    "timestamp": 1742192469480
}


简体中文
调用示例
curl --location -g --request POST "https://www.sophnet.com/api/open-apis/v1/chat/completions" \
--header "Authorization: Bearer $API_KEY" \
--header "Content-Type: application/json" \
--data-raw '{
    "messages": [
        {
            "role": "user",
            "content": "你可以帮我做什么"
        }
    ],
    "model":"DeepSeek-V3-Fast",
    "stream":false
}'

