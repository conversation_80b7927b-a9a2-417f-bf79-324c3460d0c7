const BaseModel = require('./BaseModel');

class RechargePackage extends BaseModel {
  constructor() {
    super('recharge_packages');
  }

  /**
   * 获取激活的充值套餐列表
   * @returns {Array} 套餐列表
   */
  async getActivePackages() {
    const sql = `
      SELECT id, name, description, price, balance_amount, sort_order
      FROM ${this.tableName}
      WHERE status = 'active'
      ORDER BY sort_order ASC, price ASC
    `;
    return await this.query(sql);
  }

  /**
   * 根据ID获取激活的套餐
   * @param {Number} id - 套餐ID
   * @returns {Object|null} 套餐信息
   */
  async getActivePackageById(id) {
    const sql = `
      SELECT id, name, description, price, balance_amount
      FROM ${this.tableName}
      WHERE id = ? AND status = 'active'
    `;
    const rows = await this.query(sql, [id]);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 获取推荐套餐（价格适中的套餐）
   * @param {Number} limit - 限制数量
   * @returns {Array} 推荐套餐列表
   */
  async getRecommendedPackages(limit = 3) {
    const sql = `
      SELECT id, name, description, price, balance_amount, sort_order
      FROM ${this.tableName}
      WHERE status = 'active'
      ORDER BY sort_order ASC, (balance_amount / price) DESC
      LIMIT ?
    `;
    return await this.query(sql, [limit]);
  }

  /**
   * 获取套餐统计信息
   * @returns {Object} 统计信息
   */
  async getPackageStats() {
    const sql = `
      SELECT 
        COUNT(*) as total_packages,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_packages,
        MIN(price) as min_price,
        MAX(price) as max_price,
        AVG(price) as avg_price
      FROM ${this.tableName}
    `;
    const rows = await this.query(sql);
    return rows[0];
  }

  /**
   * 检查套餐是否可购买
   * @param {Number} id - 套餐ID
   * @returns {Boolean} 是否可购买
   */
  async isPackageAvailable(id) {
    const packageInfo = await this.findOneWhere({ id, status: 'active' });
    return !!packageInfo;
  }

  /**
   * 获取套餐的性价比排序
   * @returns {Array} 按性价比排序的套餐列表
   */
  async getPackagesByValue() {
    const sql = `
      SELECT 
        id, 
        name, 
        description, 
        price, 
        balance_amount,
        ROUND(balance_amount / price, 2) as value_ratio
      FROM ${this.tableName}
      WHERE status = 'active'
      ORDER BY value_ratio DESC, sort_order ASC
    `;
    return await this.query(sql);
  }

  /**
   * 根据价格范围获取套餐
   * @param {Number} minPrice - 最低价格
   * @param {Number} maxPrice - 最高价格
   * @returns {Array} 套餐列表
   */
  async getPackagesByPriceRange(minPrice, maxPrice) {
    const sql = `
      SELECT id, name, description, price, balance_amount, sort_order
      FROM ${this.tableName}
      WHERE status = 'active' AND price >= ? AND price <= ?
      ORDER BY sort_order ASC, price ASC
    `;
    return await this.query(sql, [minPrice, maxPrice]);
  }
}

module.exports = new RechargePackage();
