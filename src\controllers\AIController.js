const OpenAIService = require('../services/OpenAIService');
const ConversationService = require('../services/ConversationService');
const { success, error, ERROR_CODES } = require('../utils/response');
const { aiConfig } = require('../config/ai');

class AIController {
  /**
   * AI服务健康检查
   */
  async healthCheck(req, res, next) {
    try {
      const health = await OpenAIService.healthCheck();
      
      success(res, {
        ai_service: health,
        config: {
          default_model: aiConfig.openai.defaultModel,
          max_tokens: aiConfig.conversation.maxTokens,
          temperature: aiConfig.conversation.temperature,
          stream_enabled: aiConfig.conversation.stream
        },
        timestamp: new Date().toISOString()
      });

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels(req, res, next) {
    try {
      const models = await OpenAIService.getAvailableModels();
      
      success(res, {
        models,
        default_model: aiConfig.openai.defaultModel,
        supported_models: aiConfig.openai.supportedModels
      });

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取模型定价信息
   */
  async getPricing(req, res, next) {
    try {
      const pricing = {};
      
      for (const model of aiConfig.openai.supportedModels) {
        const modelPricing = aiConfig.openai.modelPricing[model];
        if (modelPricing) {
          pricing[model] = {
            input_price_per_1k: modelPricing.input,
            output_price_per_1k: modelPricing.output,
            currency: 'USD'
          };
        }
      }

      success(res, {
        pricing,
        billing_config: {
          usd_to_cny_rate: aiConfig.billing.usdToCnyRate,
          service_fee_rate: aiConfig.billing.serviceFeeRate,
          min_charge_amount: aiConfig.billing.minChargeAmount
        }
      });

    } catch (err) {
      next(err);
    }
  }

  /**
   * 测试AI对话（仅开发环境）
   */
  async testChat(req, res, next) {
    try {
      if (process.env.NODE_ENV === 'production') {
        return error(res, ERROR_CODES.FORBIDDEN, '生产环境不允许测试');
      }

      const { message, model = aiConfig.openai.defaultModel } = req.body;

      if (!message) {
        return error(res, ERROR_CODES.BAD_REQUEST, '消息内容不能为空');
      }

      // 构建简单的测试消息
      const messages = [
        {
          role: 'system',
          content: '你是一个友好的AI助手，请简洁地回答用户的问题。'
        },
        {
          role: 'user',
          content: message
        }
      ];

      const response = await OpenAIService.createChatCompletion(messages, {
        model,
        maxTokens: 100,
        temperature: 0.7
      });

      success(res, {
        test_response: response,
        model_used: model,
        timestamp: new Date().toISOString()
      });

    } catch (err) {
      if (err.message.includes('OpenAI')) {
        return error(res, ERROR_CODES.EXTERNAL_SERVICE_ERROR, err.message);
      }
      next(err);
    }
  }

  /**
   * 获取AI服务统计信息
   */
  async getStats(req, res, next) {
    try {
      // TODO: 实现AI服务统计
      const stats = {
        total_requests: 0,
        total_tokens: 0,
        average_response_time: 0,
        error_rate: 0,
        popular_models: [],
        daily_usage: []
      };

      success(res, stats);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取用户AI使用统计
   */
  async getUserAIStats(req, res, next) {
    try {
      const { userId } = req.user;
      
      const stats = await ConversationService.getConversationStats(userId);
      
      success(res, stats);

    } catch (err) {
      next(err);
    }
  }
}

module.exports = new AIController();
