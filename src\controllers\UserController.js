const User = require('../models/User');
const ConsumptionLog = require('../models/ConsumptionLog');
const RechargeOrder = require('../models/RechargeOrder');
const ChatMessage = require('../models/ChatMessage');
const { success, error, ERROR_CODES, paginated } = require('../utils/response');

class UserController {
  /**
   * 获取当前用户信息
   */
  async getProfile(req, res, next) {
    try {
      const { userId } = req.user;

      const user = await User.findById(userId);
      if (!user) {
        return error(res, ERROR_CODES.NOT_FOUND, '用户不存在');
      }

      success(res, User.getPublicInfo(user));

    } catch (err) {
      next(err);
    }
  }

  /**
   * 修改密码
   */
  async changePassword(req, res, next) {
    try {
      const { userId } = req.user;
      const { current_password, new_password } = req.body;

      // 获取用户信息
      const user = await User.findById(userId);
      if (!user) {
        return error(res, ERROR_CODES.NOT_FOUND, '用户不存在');
      }

      // 验证当前密码
      const isValidPassword = await User.verifyPassword(current_password, user.password_hash);
      if (!isValidPassword) {
        return error(res, ERROR_CODES.BAD_REQUEST, '当前密码错误');
      }

      // 更新密码
      const updated = await User.updatePassword(userId, new_password);
      if (!updated) {
        return error(res, ERROR_CODES.INTERNAL_ERROR, '密码更新失败');
      }

      success(res, null, '密码修改成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取消费记录
   */
  async getConsumptionLogs(req, res, next) {
    try {
      const { userId } = req.user;
      const { page = 1, pageSize = 10, start_date, end_date } = req.query;

      const result = await ConsumptionLog.getUserConsumptionLogs(userId, {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        startDate: start_date,
        endDate: end_date
      });

      paginated(res, result.list, result.total, result.page, result.pageSize);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取充值订单记录
   */
  async getRechargeOrders(req, res, next) {
    try {
      const { userId } = req.user;
      const { page = 1, pageSize = 10, status } = req.query;

      const result = await RechargeOrder.getUserOrders(userId, {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        status
      });

      paginated(res, result.list, result.total, result.page, result.pageSize);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats(req, res, next) {
    try {
      const { userId } = req.user;

      // 获取各种统计数据
      const [
        consumptionStats,
        rechargeStats,
        tokenUsage
      ] = await Promise.all([
        ConsumptionLog.getUserConsumptionStats(userId),
        RechargeOrder.getUserRechargeStats(userId),
        ChatMessage.getUserTokenUsage(userId)
      ]);

      const stats = {
        // 消费统计
        total_consumption: consumptionStats.total_consumed || '0.0000',
        total_consumption_records: consumptionStats.total_records || 0,
        avg_consumption: consumptionStats.avg_consumption || '0.0000',
        
        // 充值统计
        total_recharge: rechargeStats.total_paid || '0.00',
        total_recharge_orders: rechargeStats.completed_orders || 0,
        last_recharge_time: rechargeStats.last_recharge_time,
        
        // Token使用统计
        total_tokens_used: tokenUsage.total_tokens || 0,
        total_ai_messages: tokenUsage.total_ai_messages || 0,
        
        // 计算节省的钱（如果直接调用API）
        estimated_api_cost: this.calculateEstimatedAPICost(tokenUsage.total_tokens || 0)
      };

      success(res, stats);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取消费统计（按时间段）
   */
  async getConsumptionStats(req, res, next) {
    try {
      const { userId } = req.user;
      const { period = 'month' } = req.query; // today, week, month, all

      const stats = await ConsumptionLog.getUserConsumptionStats(userId, { period });
      success(res, stats);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取每日消费趋势
   */
  async getDailyConsumption(req, res, next) {
    try {
      const { userId } = req.user;
      const { days = 30 } = req.query;

      const dailyData = await ConsumptionLog.getUserDailyConsumption(userId, parseInt(days));
      success(res, dailyData);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取按角色的消费统计
   */
  async getConsumptionByCharacter(req, res, next) {
    try {
      const { userId } = req.user;
      const { start_date, end_date, limit = 10 } = req.query;

      const stats = await ConsumptionLog.getUserConsumptionByCharacter(userId, {
        startDate: start_date,
        endDate: end_date,
        limit: parseInt(limit)
      });

      success(res, stats);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 更新用户资料（预留接口）
   */
  async updateProfile(req, res, next) {
    try {
      const { userId } = req.user;
      const { nickname, avatar_url } = req.body;

      // TODO: 实现用户资料更新
      // 注意：不能更新敏感信息如手机号、邮箱等
      
      success(res, null, '资料更新成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 绑定手机号（预留接口）
   */
  async bindPhone(req, res, next) {
    try {
      const { userId } = req.user;
      const { phone_number, verification_code } = req.body;

      // TODO: 实现手机号绑定逻辑
      // 1. 验证验证码
      // 2. 检查手机号是否已被其他用户使用
      // 3. 更新用户信息

      success(res, null, '手机号绑定成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 绑定邮箱（预留接口）
   */
  async bindEmail(req, res, next) {
    try {
      const { userId } = req.user;
      const { email, verification_code } = req.body;

      // TODO: 实现邮箱绑定逻辑

      success(res, null, '邮箱绑定成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 注销账户（预留接口）
   */
  async deleteAccount(req, res, next) {
    try {
      const { userId } = req.user;
      const { password, confirmation } = req.body;

      // TODO: 实现账户注销逻辑
      // 1. 验证密码
      // 2. 验证确认信息
      // 3. 清理用户数据
      // 4. 标记账户为已删除

      success(res, null, '账户注销成功');

    } catch (err) {
      next(err);
    }
  }

  /**
   * 计算预估的API成本（辅助方法）
   */
  calculateEstimatedAPICost(totalTokens) {
    // 假设API成本为每1000个token $0.002
    const costPer1000Tokens = 0.002;
    const estimatedCost = (totalTokens / 1000) * costPer1000Tokens;
    return estimatedCost.toFixed(4);
  }
}

module.exports = new UserController();
