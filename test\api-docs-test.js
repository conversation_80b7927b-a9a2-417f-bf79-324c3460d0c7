const http = require('http');

console.log('📋 API文档接口测试...\n');

// HTTP请求工具
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(body);
          resolve({ status: res.statusCode, data: json });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', () => resolve({ status: 0, data: null }));

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAPIDocs() {
  console.log('🔍 测试API文档接口...');
  
  // 测试健康检查
  const healthResult = await makeRequest('GET', '/health');
  if (healthResult.status === 200) {
    console.log('✅ 健康检查通过');
    console.log('   状态:', healthResult.data.status);
    console.log('   版本:', healthResult.data.version);
  } else {
    console.log('❌ 健康检查失败');
  }

  // 测试API文档
  const docsResult = await makeRequest('GET', '/api/v1/docs');
  if (docsResult.status === 200) {
    console.log('✅ API文档获取成功');
    console.log('   标题:', docsResult.data.title);
    console.log('   版本:', docsResult.data.version);
    console.log('   描述:', docsResult.data.description);
    console.log('   AI模型:', docsResult.data.aiModel.name);
    console.log('   特性数量:', docsResult.data.features.length);
    
    // 统计接口数量
    let totalEndpoints = 0;
    Object.keys(docsResult.data.endpoints).forEach(module => {
      const moduleEndpoints = Object.keys(docsResult.data.endpoints[module]).length;
      totalEndpoints += moduleEndpoints;
      console.log(`   ${module}模块: ${moduleEndpoints}个接口`);
    });
    console.log(`   总接口数: ${totalEndpoints}个`);
    
    // 显示限流信息
    console.log('   限流规则:');
    Object.entries(docsResult.data.rateLimits).forEach(([key, value]) => {
      console.log(`     ${key}: ${value}`);
    });
    
    // 显示流式响应格式
    console.log('   流式响应:');
    console.log(`     内容类型: ${docsResult.data.streamFormat.contentType}`);
    console.log(`     结束标记: ${docsResult.data.streamFormat.endMarker}`);
    
  } else {
    console.log('❌ API文档获取失败');
    console.log('状态码:', docsResult.status);
    console.log('响应:', docsResult.data);
  }

  console.log('\n' + '='.repeat(60));
  console.log('📋 API文档测试完成');
  console.log('='.repeat(60));
  
  if (healthResult.status === 200 && docsResult.status === 200) {
    console.log('✅ 所有测试通过！API文档已更新');
    console.log('\n🌐 访问方式:');
    console.log('• 健康检查: http://localhost:3000/health');
    console.log('• API文档: http://localhost:3000/api/v1/docs');
    console.log('• 完整文档: 查看 API_DOCUMENTATION.md');
    
    console.log('\n📊 系统信息:');
    console.log('• 系统版本: v1.2.0');
    console.log('• AI模型: DeepSeek V3 Fast');
    console.log('• 支持流式响应: ✅');
    console.log('• Token精确计费: ✅');
    console.log('• 企业级安全: ✅');
  } else {
    console.log('❌ 部分测试失败，请检查服务器状态');
  }
}

testAPIDocs().catch(console.error);
