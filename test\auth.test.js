const { app, request, TEST_USERS, testUtils } = require('./setup');

describe('认证接口测试', () => {
  // 每个测试后清理数据
  afterEach(async () => {
    await testUtils.cleanupTestData();
  });

  describe('POST /api/v1/auth/register - 用户注册', () => {
    test('邮箱注册成功', async () => {
      const userData = {
        email: TEST_USERS.user1.email,
        password: TEST_USERS.user1.password
      };

      const res = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.message).toBe('注册成功');
      expect(res.body.data).toHaveProperty('access_token');
      expect(res.body.data).toHaveProperty('user');
      expect(res.body.data.user.email).toContain('***@');
      expect(res.body.data.user.balance).toBe('10.0000'); // 新用户赠送余额
    });

    test('手机号注册成功', async () => {
      const userData = {
        phone_number: TEST_USERS.user1.phone_number,
        password: TEST_USERS.user1.password,
        verification_code: '123456' // 模拟验证码
      };

      const res = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data.user.phone_number).toContain('****');
    });

    test('重复邮箱注册失败', async () => {
      // 先注册一个用户
      await testUtils.createUserAndGetToken(TEST_USERS.user1);

      // 再次注册相同邮箱
      const res = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: TEST_USERS.user1.email,
          password: 'different123'
        })
        .expect(409);

      expect(res.body.code).toBe(40901);
      expect(res.body.message).toContain('已被注册');
    });

    test('无效邮箱格式', async () => {
      const res = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: 'invalid-email',
          password: 'password123'
        })
        .expect(400);

      expect(res.body.code).toBe(40001);
    });

    test('密码太短', async () => {
      const res = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: '<EMAIL>',
          password: '123'
        })
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('POST /api/v1/auth/login - 用户登录', () => {
    beforeEach(async () => {
      // 创建测试用户
      await testUtils.createUserAndGetToken(TEST_USERS.user1);
    });

    test('邮箱登录成功', async () => {
      const res = await request(app)
        .post('/api/v1/auth/login')
        .send({
          identity: TEST_USERS.user1.email,
          password: TEST_USERS.user1.password
        })
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.message).toBe('登录成功');
      expect(res.body.data).toHaveProperty('access_token');
      expect(res.body.data).toHaveProperty('user');
    });

    test('手机号登录成功', async () => {
      const res = await request(app)
        .post('/api/v1/auth/login')
        .send({
          identity: TEST_USERS.user1.phone_number,
          password: TEST_USERS.user1.password
        })
        .expect(200);

      expect(res.body.code).toBe(0);
    });

    test('错误密码登录失败', async () => {
      const res = await request(app)
        .post('/api/v1/auth/login')
        .send({
          identity: TEST_USERS.user1.email,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(res.body.code).toBe(40102);
      expect(res.body.message).toContain('用户名或密码错误');
    });

    test('不存在的用户登录失败', async () => {
      const res = await request(app)
        .post('/api/v1/auth/login')
        .send({
          identity: '<EMAIL>',
          password: 'password123'
        })
        .expect(401);

      expect(res.body.code).toBe(40102);
    });
  });

  describe('GET /api/v1/auth/me - 获取当前用户信息', () => {
    beforeEach(async () => {
      await testUtils.createUserAndGetToken(TEST_USERS.user1);
    });

    test('获取用户信息成功', async () => {
      const res = await request(app)
        .get('/api/v1/auth/me')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('uid');
      expect(res.body.data).toHaveProperty('balance');
      expect(res.body.data).not.toHaveProperty('password_hash');
    });

    test('无Token访问失败', async () => {
      const res = await request(app)
        .get('/api/v1/auth/me')
        .expect(401);

      expect(res.body.code).toBe(40101);
    });

    test('无效Token访问失败', async () => {
      const res = await request(app)
        .get('/api/v1/auth/me')
        .set({ Authorization: 'Bearer invalid-token' })
        .expect(401);

      expect(res.body.code).toBe(40101);
    });
  });

  describe('POST /api/v1/auth/logout - 用户登出', () => {
    beforeEach(async () => {
      await testUtils.createUserAndGetToken(TEST_USERS.user1);
    });

    test('登出成功', async () => {
      const res = await request(app)
        .post('/api/v1/auth/logout')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.message).toBe('登出成功');
    });
  });

  describe('POST /api/v1/auth/refresh - 刷新Token', () => {
    beforeEach(async () => {
      await testUtils.createUserAndGetToken(TEST_USERS.user1);
    });

    test('刷新Token成功', async () => {
      const res = await request(app)
        .post('/api/v1/auth/refresh')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('access_token');
      expect(res.body.message).toBe('Token刷新成功');
    });
  });
});
