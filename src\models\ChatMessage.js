const BaseModel = require('./BaseModel');

class ChatMessage extends BaseModel {
  constructor() {
    super('chat_messages');
  }

  /**
   * 创建用户消息
   * @param {Number} sessionId - 会话ID
   * @param {String} content - 消息内容
   * @returns {Object} 创建的消息
   */
  async createUserMessage(sessionId, content) {
    return await this.create({
      session_id: sessionId,
      sender_type: 'user',
      content,
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    });
  }

  /**
   * 创建AI消息
   * @param {Number} sessionId - 会话ID
   * @param {String} content - 消息内容
   * @param {Object} tokenUsage - Token使用情况
   * @returns {Object} 创建的消息
   */
  async createAIMessage(sessionId, content, tokenUsage = {}) {
    const {
      prompt_tokens = 0,
      completion_tokens = 0,
      total_tokens = 0
    } = tokenUsage;

    return await this.create({
      session_id: sessionId,
      sender_type: 'ai',
      content,
      prompt_tokens,
      completion_tokens,
      total_tokens
    });
  }

  /**
   * 获取会话历史消息
   * @param {Number} sessionId - 会话ID
   * @param {Object} options - 查询选项
   * @returns {Array} 消息列表
   */
  async getSessionMessages(sessionId, options = {}) {
    const {
      lastMessageId = null,
      limit = 20,
      orderDirection = 'DESC'
    } = options;

    let sql = `
      SELECT id, sender_type, content, prompt_tokens, completion_tokens, total_tokens, created_at
      FROM ${this.tableName}
      WHERE session_id = ?
    `;
    const params = [sessionId];

    // 如果指定了lastMessageId，则获取该消息之前的消息（用于分页加载）
    if (lastMessageId) {
      sql += ` AND id < ?`;
      params.push(lastMessageId);
    }

    sql += ` ORDER BY created_at ${orderDirection} LIMIT ?`;
    params.push(limit);

    const messages = await this.query(sql, params);

    // 如果是倒序查询，需要反转结果以保持时间顺序
    if (orderDirection === 'DESC') {
      messages.reverse();
    }

    return messages;
  }

  /**
   * 获取最近的对话上下文
   * @param {Number} sessionId - 会话ID
   * @param {Number} limit - 限制数量
   * @returns {Array} 消息列表
   */
  async getRecentContext(sessionId, limit = 10) {
    const sql = `
      SELECT sender_type, content, created_at
      FROM ${this.tableName}
      WHERE session_id = ?
      ORDER BY created_at DESC
      LIMIT ?
    `;

    const messages = await this.query(sql, [sessionId, limit]);
    return messages.reverse(); // 按时间正序返回
  }

  /**
   * 获取会话消息统计
   * @param {Number} sessionId - 会话ID
   * @returns {Object} 统计信息
   */
  async getSessionMessageStats(sessionId) {
    const sql = `
      SELECT 
        COUNT(*) as total_messages,
        COUNT(CASE WHEN sender_type = 'user' THEN 1 END) as user_messages,
        COUNT(CASE WHEN sender_type = 'ai' THEN 1 END) as ai_messages,
        SUM(total_tokens) as total_tokens_used,
        MAX(created_at) as last_message_time
      FROM ${this.tableName}
      WHERE session_id = ?
    `;

    const rows = await this.query(sql, [sessionId]);
    return rows[0];
  }

  /**
   * 删除会话的所有消息
   * @param {Number} sessionId - 会话ID
   * @returns {Boolean} 是否删除成功
   */
  async deleteSessionMessages(sessionId) {
    const sql = `DELETE FROM ${this.tableName} WHERE session_id = ?`;
    const result = await this.query(sql, [sessionId]);
    return result.affectedRows >= 0;
  }

  /**
   * 搜索消息内容
   * @param {Number} sessionId - 会话ID
   * @param {String} keyword - 搜索关键词
   * @param {Object} options - 查询选项
   * @returns {Array} 匹配的消息
   */
  async searchMessages(sessionId, keyword, options = {}) {
    const {
      limit = 50,
      senderType = null
    } = options;

    let sql = `
      SELECT id, sender_type, content, created_at
      FROM ${this.tableName}
      WHERE session_id = ? AND content LIKE ?
    `;
    const params = [sessionId, `%${keyword}%`];

    if (senderType) {
      sql += ` AND sender_type = ?`;
      params.push(senderType);
    }

    sql += ` ORDER BY created_at DESC LIMIT ?`;
    params.push(limit);

    return await this.query(sql, params);
  }

  /**
   * 获取用户总Token消耗
   * @param {Number} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Object} Token统计
   */
  async getUserTokenUsage(userId, options = {}) {
    const {
      startDate = null,
      endDate = null
    } = options;

    let sql = `
      SELECT 
        SUM(cm.total_tokens) as total_tokens,
        SUM(cm.prompt_tokens) as total_prompt_tokens,
        SUM(cm.completion_tokens) as total_completion_tokens,
        COUNT(cm.id) as total_ai_messages
      FROM ${this.tableName} cm
      JOIN chat_sessions cs ON cm.session_id = cs.id
      WHERE cs.user_id = ? AND cm.sender_type = 'ai'
    `;
    const params = [userId];

    if (startDate) {
      sql += ` AND cm.created_at >= ?`;
      params.push(startDate);
    }

    if (endDate) {
      sql += ` AND cm.created_at <= ?`;
      params.push(endDate);
    }

    const rows = await this.query(sql, params);
    return rows[0];
  }
}

module.exports = new ChatMessage();
