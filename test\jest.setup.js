// Jest全局设置文件
const { testUtils } = require('./setup');

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key';
process.env.DB_NAME = 'tree_hole_chat_test';

// 全局测试钩子
beforeAll(async () => {
  console.log('🚀 开始运行API测试套件...');
  
  // 等待应用启动
  await new Promise(resolve => setTimeout(resolve, 2000));
});

afterAll(async () => {
  console.log('🧹 清理测试环境...');
  
  // 清理所有测试数据
  await testUtils.cleanupTestData();
  
  // 关闭数据库连接
  const { pool } = require('../src/config/database');
  if (pool) {
    await pool.end();
  }
  
  console.log('✅ 测试套件运行完成');
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('测试中发生未处理的Promise拒绝:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('测试中发生未捕获的异常:', error);
});

// 扩展Jest匹配器
expect.extend({
  toBeValidTimestamp(received) {
    const pass = !isNaN(Date.parse(received));
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid timestamp`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid timestamp`,
        pass: false,
      };
    }
  },
  
  toBeValidUUID(received) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = typeof received === 'string' && uuidRegex.test(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid UUID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass: false,
      };
    }
  },
  
  toBeValidJWT(received) {
    const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    const pass = typeof received === 'string' && jwtRegex.test(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid JWT`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid JWT`,
        pass: false,
      };
    }
  }
});
