const http = require('http');
const https = require('https');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'password123'
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求工具
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 测试用例
const tests = [
  {
    name: '健康检查',
    method: 'GET',
    path: '/health',
    expectedStatus: 200,
    validate: (res) => res.body.status === 'ok'
  },
  {
    name: '获取AI角色列表',
    method: 'GET',
    path: '/api/v1/ai-characters',
    expectedStatus: 200,
    validate: (res) => res.body.code === 0 && Array.isArray(res.body.data.list)
  },
  {
    name: '获取充值套餐',
    method: 'GET',
    path: '/api/v1/recharge-packages',
    expectedStatus: 200,
    validate: (res) => res.body.code === 0 && Array.isArray(res.body.data)
  },
  {
    name: '用户注册',
    method: 'POST',
    path: '/api/v1/auth/register',
    data: TEST_USER,
    expectedStatus: 200,
    validate: (res) => res.body.code === 0 && res.body.data.access_token,
    saveToken: true
  },
  {
    name: '获取用户信息',
    method: 'GET',
    path: '/api/v1/user/profile',
    requireAuth: true,
    expectedStatus: 200,
    validate: (res) => res.body.code === 0 && res.body.data.uid
  },
  {
    name: '获取会话列表',
    method: 'GET',
    path: '/api/v1/chat/sessions',
    requireAuth: true,
    expectedStatus: 200,
    validate: (res) => res.body.code === 0 && Array.isArray(res.body.data.list)
  },
  {
    name: '获取支付方式',
    method: 'GET',
    path: '/api/v1/payment/methods',
    requireAuth: true,
    expectedStatus: 200,
    validate: (res) => res.body.code === 0 && Array.isArray(res.body.data)
  }
];

// 运行测试
async function runTests() {
  colorLog('cyan', '🚀 开始运行API测试...\n');
  
  let token = null;
  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const headers = {};
      if (test.requireAuth && token) {
        headers.Authorization = `Bearer ${token}`;
      }

      colorLog('blue', `📋 测试: ${test.name}`);
      
      const response = await makeRequest(test.method, test.path, test.data, headers);
      
      // 检查状态码
      if (response.status !== test.expectedStatus) {
        colorLog('red', `❌ 状态码错误: 期望 ${test.expectedStatus}, 实际 ${response.status}`);
        console.log('响应:', response.body);
        failed++;
        continue;
      }

      // 运行自定义验证
      if (test.validate && !test.validate(response)) {
        colorLog('red', `❌ 验证失败`);
        console.log('响应:', response.body);
        failed++;
        continue;
      }

      // 保存token
      if (test.saveToken && response.body.data && response.body.data.access_token) {
        token = response.body.data.access_token;
        colorLog('yellow', `🔑 已保存认证Token`);
      }

      colorLog('green', `✅ 通过`);
      passed++;

    } catch (error) {
      colorLog('red', `❌ 请求失败: ${error.message}`);
      failed++;
    }
    
    console.log(''); // 空行
  }

  // 输出结果
  console.log('='.repeat(50));
  colorLog('cyan', '📊 测试结果摘要');
  console.log('='.repeat(50));
  colorLog('green', `✅ 通过: ${passed}/${tests.length}`);
  if (failed > 0) {
    colorLog('red', `❌ 失败: ${failed}/${tests.length}`);
  }
  
  if (failed === 0) {
    colorLog('green', '\n🎉 所有测试都通过了！API已准备好交付给前端团队。');
  } else {
    colorLog('red', '\n💥 部分测试失败，请检查并修复问题。');
  }

  // 清理测试数据
  if (token) {
    try {
      colorLog('yellow', '\n🧹 清理测试数据...');
      // 这里可以添加清理逻辑，比如删除测试用户
    } catch (error) {
      colorLog('yellow', '清理数据时出错，请手动清理');
    }
  }
}

// 运行测试
runTests().catch(error => {
  colorLog('red', `❌ 测试运行失败: ${error.message}`);
  process.exit(1);
});
