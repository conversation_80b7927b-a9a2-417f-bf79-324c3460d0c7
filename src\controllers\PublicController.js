const AICharacter = require('../models/AICharacter');
const RechargePackage = require('../models/RechargePackage');
const { success, error, ERROR_CODES, paginated } = require('../utils/response');

class PublicController {
  /**
   * 获取AI角色列表
   */
  async getAICharacters(req, res, next) {
    try {
      const { page = 1, pageSize = 10 } = req.query;

      const result = await AICharacter.getOnlineCharacters({
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      });

      paginated(res, result.list, result.total, result.page, result.pageSize);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取单个AI角色详情
   */
  async getAICharacterDetail(req, res, next) {
    try {
      const { id } = req.params;

      const character = await AICharacter.getCharacterDetail(parseInt(id));
      if (!character) {
        return error(res, ERROR_CODES.NOT_FOUND, '角色不存在或已下线');
      }

      // 对于公开接口，不返回完整的system_prompt，只返回部分信息
      const publicCharacterInfo = {
        id: character.id,
        name: character.name,
        avatar_url: character.avatar_url,
        description: character.description,
        popularity: character.popularity,
        sort_order: character.sort_order,
        created_at: character.created_at,
        updated_at: character.updated_at
      };

      success(res, publicCharacterInfo);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取热门AI角色
   */
  async getPopularCharacters(req, res, next) {
    try {
      const { limit = 10 } = req.query;

      const characters = await AICharacter.getPopularCharacters(parseInt(limit));
      success(res, characters);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 搜索AI角色
   */
  async searchCharacters(req, res, next) {
    try {
      const { keyword, page = 1, pageSize = 10 } = req.query;

      if (!keyword || keyword.trim().length === 0) {
        return error(res, ERROR_CODES.BAD_REQUEST, '搜索关键词不能为空');
      }

      const result = await AICharacter.searchCharacters(keyword.trim(), {
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      });

      paginated(res, result.list, result.total, result.page, result.pageSize);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取充值套餐列表
   */
  async getRechargePackages(req, res, next) {
    try {
      const packages = await RechargePackage.getActivePackages();
      success(res, packages);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取推荐充值套餐
   */
  async getRecommendedPackages(req, res, next) {
    try {
      const { limit = 3 } = req.query;

      const packages = await RechargePackage.getRecommendedPackages(parseInt(limit));
      success(res, packages);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取套餐按性价比排序
   */
  async getPackagesByValue(req, res, next) {
    try {
      const packages = await RechargePackage.getPackagesByValue();
      success(res, packages);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 根据价格范围获取套餐
   */
  async getPackagesByPriceRange(req, res, next) {
    try {
      const { minPrice, maxPrice } = req.query;

      if (!minPrice || !maxPrice) {
        return error(res, ERROR_CODES.BAD_REQUEST, '请提供价格范围');
      }

      const packages = await RechargePackage.getPackagesByPriceRange(
        parseFloat(minPrice),
        parseFloat(maxPrice)
      );

      success(res, packages);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取网站统计信息（公开数据）
   */
  async getSiteStats(req, res, next) {
    try {
      // TODO: 实现网站统计信息
      // 这里可以返回一些公开的统计数据，如：
      // - 在线角色数量
      // - 总对话次数（脱敏）
      // - 用户满意度等

      const stats = {
        online_characters: 0, // await AICharacter.count({ status: 'online' })
        total_conversations: 0, // 脱敏的对话总数
        user_satisfaction: 4.8, // 用户满意度评分
        active_users: 0 // 活跃用户数（脱敏）
      };

      success(res, stats);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取系统公告（预留接口）
   */
  async getAnnouncements(req, res, next) {
    try {
      // TODO: 实现系统公告功能
      const announcements = [];

      success(res, announcements);

    } catch (err) {
      next(err);
    }
  }

  /**
   * 获取帮助文档（预留接口）
   */
  async getHelpDocs(req, res, next) {
    try {
      // TODO: 实现帮助文档功能
      const helpDocs = [];

      success(res, helpDocs);

    } catch (err) {
      next(err);
    }
  }
}

module.exports = new PublicController();
