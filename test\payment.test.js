const { app, request, TEST_USERS, testUtils } = require('./setup');

describe('支付接口测试', () => {
  let packageId;

  beforeAll(async () => {
    // 获取可用的充值套餐
    const packagesRes = await request(app)
      .get('/api/v1/recharge-packages')
      .expect(200);

    if (packagesRes.body.data.length > 0) {
      packageId = packagesRes.body.data[0].id;
    } else {
      console.warn('没有可用的充值套餐，跳过支付测试');
    }
  });

  beforeEach(async () => {
    await testUtils.createUserAndGetToken(TEST_USERS.user1);
  });

  afterEach(async () => {
    await testUtils.cleanupTestData();
  });

  describe('POST /api/v1/payment/orders - 创建充值订单', () => {
    test('支付宝创建订单成功', async () => {
      if (!packageId) {
        console.warn('跳过创建订单测试：没有可用套餐');
        return;
      }

      const orderData = {
        package_id: packageId,
        payment_method: 'alipay'
      };

      const res = await request(app)
        .post('/api/v1/payment/orders')
        .set(testUtils.getAuthHeader())
        .send(orderData)
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('order_sn');
      expect(res.body.data).toHaveProperty('pay_url');
      expect(res.body.data).toHaveProperty('amount');
      expect(res.body.data).toHaveProperty('package_name');
      expect(res.body.data).toHaveProperty('expires_at');

      // 保存订单号供其他测试使用
      global.testData.orders.test_order = res.body.data.order_sn;
    });

    test('微信支付创建订单成功', async () => {
      if (!packageId) return;

      const orderData = {
        package_id: packageId,
        payment_method: 'wechat_pay'
      };

      const res = await request(app)
        .post('/api/v1/payment/orders')
        .set(testUtils.getAuthHeader())
        .send(orderData)
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data.pay_url).toContain('weixin://');
    });

    test('不存在的套餐创建订单', async () => {
      const orderData = {
        package_id: 99999,
        payment_method: 'alipay'
      };

      const res = await request(app)
        .post('/api/v1/payment/orders')
        .set(testUtils.getAuthHeader())
        .send(orderData)
        .expect(404);

      expect(res.body.code).toBe(40401);
      expect(res.body.message).toContain('不存在');
    });

    test('不支持的支付方式', async () => {
      if (!packageId) return;

      const orderData = {
        package_id: packageId,
        payment_method: 'unsupported_method'
      };

      const res = await request(app)
        .post('/api/v1/payment/orders')
        .set(testUtils.getAuthHeader())
        .send(orderData)
        .expect(400);

      expect(res.body.code).toBe(40001);
    });

    test('未认证创建订单', async () => {
      if (!packageId) return;

      const orderData = {
        package_id: packageId,
        payment_method: 'alipay'
      };

      const res = await request(app)
        .post('/api/v1/payment/orders')
        .send(orderData)
        .expect(401);

      expect(res.body.code).toBe(40101);
    });
  });

  describe('GET /api/v1/payment/orders/:order_sn - 查询订单状态', () => {
    let testOrderSn;

    beforeEach(async () => {
      if (!packageId) return;

      // 创建测试订单
      const orderRes = await request(app)
        .post('/api/v1/payment/orders')
        .set(testUtils.getAuthHeader())
        .send({
          package_id: packageId,
          payment_method: 'alipay'
        })
        .expect(200);

      testOrderSn = orderRes.body.data.order_sn;
    });

    test('查询订单状态成功', async () => {
      if (!testOrderSn) return;

      const res = await request(app)
        .get(`/api/v1/payment/orders/${testOrderSn}`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('order_sn');
      expect(res.body.data).toHaveProperty('status');
      expect(res.body.data).toHaveProperty('amount_paid');
      expect(res.body.data).toHaveProperty('balance_granted');
      expect(res.body.data).toHaveProperty('payment_method');
      expect(res.body.data).toHaveProperty('created_at');
      expect(res.body.data).toHaveProperty('package_name');
    });

    test('查询不存在的订单', async () => {
      const res = await request(app)
        .get('/api/v1/payment/orders/NONEXISTENT123')
        .set(testUtils.getAuthHeader())
        .expect(404);

      expect(res.body.code).toBe(40401);
      expect(res.body.message).toContain('不存在');
    });

    test('查询其他用户的订单', async () => {
      if (!testOrderSn) return;

      // 创建另一个用户
      await testUtils.createUserAndGetToken(TEST_USERS.user2, 'user2');

      const res = await request(app)
        .get(`/api/v1/payment/orders/${testOrderSn}`)
        .set(testUtils.getAuthHeader('user2'))
        .expect(404);

      expect(res.body.code).toBe(40401);
    });
  });

  describe('PUT /api/v1/payment/orders/:order_sn/cancel - 取消订单', () => {
    let testOrderSn;

    beforeEach(async () => {
      if (!packageId) return;

      const orderRes = await request(app)
        .post('/api/v1/payment/orders')
        .set(testUtils.getAuthHeader())
        .send({
          package_id: packageId,
          payment_method: 'alipay'
        })
        .expect(200);

      testOrderSn = orderRes.body.data.order_sn;
    });

    test('取消订单成功', async () => {
      if (!testOrderSn) return;

      const res = await request(app)
        .put(`/api/v1/payment/orders/${testOrderSn}/cancel`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.message).toBe('订单取消成功');

      // 验证订单状态已更新
      const statusRes = await request(app)
        .get(`/api/v1/payment/orders/${testOrderSn}`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(statusRes.body.data.status).toBe('cancelled');
    });

    test('取消不存在的订单', async () => {
      const res = await request(app)
        .put('/api/v1/payment/orders/NONEXISTENT123/cancel')
        .set(testUtils.getAuthHeader())
        .expect(404);

      expect(res.body.code).toBe(40401);
    });
  });

  describe('POST /api/v1/payment/orders/:order_sn/repay - 重新支付', () => {
    let testOrderSn;

    beforeEach(async () => {
      if (!packageId) return;

      const orderRes = await request(app)
        .post('/api/v1/payment/orders')
        .set(testUtils.getAuthHeader())
        .send({
          package_id: packageId,
          payment_method: 'alipay'
        })
        .expect(200);

      testOrderSn = orderRes.body.data.order_sn;
    });

    test('重新支付成功', async () => {
      if (!testOrderSn) return;

      const res = await request(app)
        .post(`/api/v1/payment/orders/${testOrderSn}/repay`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('order_sn');
      expect(res.body.data).toHaveProperty('pay_url');
      expect(res.body.data).toHaveProperty('amount');
    });

    test('重新支付不存在的订单', async () => {
      const res = await request(app)
        .post('/api/v1/payment/orders/NONEXISTENT123/repay')
        .set(testUtils.getAuthHeader())
        .expect(404);

      expect(res.body.code).toBe(40401);
    });
  });

  describe('GET /api/v1/payment/methods - 获取支付方式', () => {
    test('获取支付方式成功', async () => {
      const res = await request(app)
        .get('/api/v1/payment/methods')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);

      if (res.body.data.length > 0) {
        const method = res.body.data[0];
        expect(method).toHaveProperty('code');
        expect(method).toHaveProperty('name');
        expect(method).toHaveProperty('icon');
        expect(method).toHaveProperty('enabled');
      }
    });
  });

  describe('支付回调接口', () => {
    describe('POST /api/v1/payment/notify/alipay - 支付宝回调', () => {
      test('支付宝回调处理', async () => {
        const callbackData = {
          out_trade_no: 'TEST_ORDER_123',
          trade_status: 'TRADE_SUCCESS',
          trade_no: 'ALIPAY_TRADE_123'
        };

        const res = await request(app)
          .post('/api/v1/payment/notify/alipay')
          .send(callbackData);

        // 由于是模拟回调，预期返回fail（因为订单不存在）
        expect(res.text).toBe('fail');
      });

      test('支付宝回调失败状态', async () => {
        const callbackData = {
          out_trade_no: 'TEST_ORDER_123',
          trade_status: 'TRADE_CLOSED',
          trade_no: 'ALIPAY_TRADE_123'
        };

        const res = await request(app)
          .post('/api/v1/payment/notify/alipay')
          .send(callbackData);

        expect(res.text).toBe('fail');
      });
    });

    describe('POST /api/v1/payment/notify/wechat_pay - 微信支付回调', () => {
      test('微信支付回调处理', async () => {
        const res = await request(app)
          .post('/api/v1/payment/notify/wechat_pay')
          .send('<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>');

        expect(res.text).toContain('<return_code><![CDATA[SUCCESS]]></return_code>');
      });
    });
  });
});
