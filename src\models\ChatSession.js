const BaseModel = require('./BaseModel');

class ChatSession extends BaseModel {
  constructor() {
    super('chat_sessions');
  }

  /**
   * 获取或创建聊天会话
   * @param {Number} userId - 用户ID
   * @param {Number} characterId - 角色ID
   * @returns {Object} 会话对象
   */
  async getOrCreateSession(userId, characterId) {
    // 先尝试查找现有会话
    let session = await this.findOneWhere({
      user_id: userId,
      character_id: characterId
    });

    // 如果不存在则创建新会话
    if (!session) {
      session = await this.create({
        user_id: userId,
        character_id: characterId
      });
    } else {
      // 更新会话的最后活跃时间
      session = await this.update(session.id, {
        updated_at: new Date()
      });
    }

    return session;
  }

  /**
   * 获取用户的会话列表
   * @param {Number} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Object} 分页结果
   */
  async getUserSessions(userId, options = {}) {
    const {
      page = 1,
      pageSize = 10
    } = options;

    const offset = (page - 1) * pageSize;

    const sql = `
      SELECT 
        cs.id,
        cs.user_id,
        cs.character_id,
        cs.created_at,
        cs.updated_at,
        ac.name as character_name,
        ac.avatar_url as character_avatar,
        ac.description as character_description,
        (
          SELECT content 
          FROM chat_messages cm 
          WHERE cm.session_id = cs.id 
          ORDER BY cm.created_at DESC 
          LIMIT 1
        ) as last_message,
        (
          SELECT created_at 
          FROM chat_messages cm 
          WHERE cm.session_id = cs.id 
          ORDER BY cm.created_at DESC 
          LIMIT 1
        ) as last_message_time
      FROM ${this.tableName} cs
      LEFT JOIN ai_characters ac ON cs.character_id = ac.id
      WHERE cs.user_id = ?
      ORDER BY cs.updated_at DESC
      LIMIT ? OFFSET ?
    `;

    const countSql = `
      SELECT COUNT(*) as count 
      FROM ${this.tableName} 
      WHERE user_id = ?
    `;

    const [list, countResult] = await Promise.all([
      this.query(sql, [userId, pageSize, offset]),
      this.query(countSql, [userId])
    ]);

    // 格式化返回数据
    const formattedList = list.map(session => ({
      session_id: session.id,
      character: {
        id: session.character_id,
        name: session.character_name,
        avatar_url: session.character_avatar,
        description: session.character_description
      },
      last_message: session.last_message || '暂无消息',
      last_message_time: session.last_message_time || session.created_at,
      created_at: session.created_at,
      updated_at: session.updated_at
    }));

    return {
      list: formattedList,
      total: countResult[0].count,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    };
  }

  /**
   * 获取会话详情（包含角色信息）
   * @param {Number} sessionId - 会话ID
   * @param {Number} userId - 用户ID（用于权限验证）
   * @returns {Object|null} 会话详情
   */
  async getSessionDetail(sessionId, userId) {
    const sql = `
      SELECT 
        cs.*,
        ac.name as character_name,
        ac.avatar_url as character_avatar,
        ac.description as character_description,
        ac.system_prompt
      FROM ${this.tableName} cs
      LEFT JOIN ai_characters ac ON cs.character_id = ac.id
      WHERE cs.id = ? AND cs.user_id = ?
    `;

    const rows = await this.query(sql, [sessionId, userId]);
    if (rows.length === 0) return null;

    const session = rows[0];
    return {
      id: session.id,
      user_id: session.user_id,
      character_id: session.character_id,
      character: {
        id: session.character_id,
        name: session.character_name,
        avatar_url: session.character_avatar,
        description: session.character_description,
        system_prompt: session.system_prompt
      },
      created_at: session.created_at,
      updated_at: session.updated_at
    };
  }

  /**
   * 删除用户会话
   * @param {Number} sessionId - 会话ID
   * @param {Number} userId - 用户ID（用于权限验证）
   * @returns {Boolean} 是否删除成功
   */
  async deleteUserSession(sessionId, userId) {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ? AND user_id = ?`;
    const result = await this.query(sql, [sessionId, userId]);
    return result.affectedRows > 0;
  }

  /**
   * 获取会话统计信息
   * @param {Number} userId - 用户ID
   * @returns {Object} 统计信息
   */
  async getSessionStats(userId) {
    const sql = `
      SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_sessions
      FROM ${this.tableName}
      WHERE user_id = ?
    `;

    const rows = await this.query(sql, [userId]);
    return rows[0];
  }
}

module.exports = new ChatSession();
