const { app, request, TEST_USERS, testUtils } = require('./setup');

describe('用户中心接口测试', () => {
  beforeEach(async () => {
    await testUtils.createUserAndGetToken(TEST_USERS.user1);
  });

  afterEach(async () => {
    await testUtils.cleanupTestData();
  });

  describe('GET /api/v1/user/profile - 获取用户信息', () => {
    test('获取用户信息成功', async () => {
      const res = await request(app)
        .get('/api/v1/user/profile')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('uid');
      expect(res.body.data).toHaveProperty('balance');
      expect(res.body.data).toHaveProperty('role');
      expect(res.body.data).toHaveProperty('status');
      expect(res.body.data).toHaveProperty('created_at');
      expect(res.body.data).not.toHaveProperty('password_hash');
      
      // 检查敏感信息脱敏
      if (res.body.data.email) {
        expect(res.body.data.email).toContain('***@');
      }
      if (res.body.data.phone_number) {
        expect(res.body.data.phone_number).toContain('****');
      }
    });

    test('未认证获取用户信息', async () => {
      const res = await request(app)
        .get('/api/v1/user/profile')
        .expect(401);

      expect(res.body.code).toBe(40101);
    });
  });

  describe('PUT /api/v1/user/password - 修改密码', () => {
    test('修改密码成功', async () => {
      const passwordData = {
        current_password: TEST_USERS.user1.password,
        new_password: 'newpassword123'
      };

      const res = await request(app)
        .put('/api/v1/user/password')
        .set(testUtils.getAuthHeader())
        .send(passwordData)
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.message).toBe('密码修改成功');

      // 验证新密码可以登录
      const loginRes = await request(app)
        .post('/api/v1/auth/login')
        .send({
          identity: TEST_USERS.user1.email,
          password: 'newpassword123'
        })
        .expect(200);

      expect(loginRes.body.code).toBe(0);
    });

    test('当前密码错误', async () => {
      const passwordData = {
        current_password: 'wrongpassword',
        new_password: 'newpassword123'
      };

      const res = await request(app)
        .put('/api/v1/user/password')
        .set(testUtils.getAuthHeader())
        .send(passwordData)
        .expect(400);

      expect(res.body.code).toBe(40001);
      expect(res.body.message).toContain('当前密码错误');
    });

    test('新密码太短', async () => {
      const passwordData = {
        current_password: TEST_USERS.user1.password,
        new_password: '123'
      };

      const res = await request(app)
        .put('/api/v1/user/password')
        .set(testUtils.getAuthHeader())
        .send(passwordData)
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/user/consumption-logs - 获取消费记录', () => {
    test('获取消费记录成功', async () => {
      const res = await request(app)
        .get('/api/v1/user/consumption-logs')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('list');
      expect(res.body.data).toHaveProperty('total');
      expect(res.body.data).toHaveProperty('page');
      expect(res.body.data).toHaveProperty('pageSize');
      expect(Array.isArray(res.body.data.list)).toBe(true);
    });

    test('分页获取消费记录', async () => {
      const res = await request(app)
        .get('/api/v1/user/consumption-logs?page=1&pageSize=5')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.data.page).toBe(1);
      expect(res.body.data.pageSize).toBe(5);
    });

    test('按日期范围获取消费记录', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const res = await request(app)
        .get(`/api/v1/user/consumption-logs?start_date=${startDate}&end_date=${endDate}`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
    });
  });

  describe('GET /api/v1/user/recharge-orders - 获取充值记录', () => {
    test('获取充值记录成功', async () => {
      const res = await request(app)
        .get('/api/v1/user/recharge-orders')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('list');
      expect(res.body.data).toHaveProperty('total');
      expect(res.body.data).toHaveProperty('page');
      expect(res.body.data).toHaveProperty('pageSize');
      expect(Array.isArray(res.body.data.list)).toBe(true);
    });

    test('按状态筛选充值记录', async () => {
      const res = await request(app)
        .get('/api/v1/user/recharge-orders?status=completed')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      // 如果有记录，检查状态筛选
      res.body.data.list.forEach(order => {
        expect(order.status).toBe('completed');
      });
    });

    test('无效状态筛选', async () => {
      const res = await request(app)
        .get('/api/v1/user/recharge-orders?status=invalid_status')
        .set(testUtils.getAuthHeader())
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/user/stats - 获取用户统计', () => {
    test('获取用户统计成功', async () => {
      const res = await request(app)
        .get('/api/v1/user/stats')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('total_consumption');
      expect(res.body.data).toHaveProperty('total_consumption_records');
      expect(res.body.data).toHaveProperty('avg_consumption');
      expect(res.body.data).toHaveProperty('total_recharge');
      expect(res.body.data).toHaveProperty('total_recharge_orders');
      expect(res.body.data).toHaveProperty('total_tokens_used');
      expect(res.body.data).toHaveProperty('total_ai_messages');
      expect(res.body.data).toHaveProperty('estimated_api_cost');
    });
  });

  describe('GET /api/v1/user/consumption-stats - 获取消费统计', () => {
    test('获取月度消费统计', async () => {
      const res = await request(app)
        .get('/api/v1/user/consumption-stats?period=month')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(res.body.data).toHaveProperty('total_records');
      expect(res.body.data).toHaveProperty('total_consumed');
      expect(res.body.data).toHaveProperty('total_tokens');
      expect(res.body.data).toHaveProperty('avg_consumption');
    });

    test('获取今日消费统计', async () => {
      const res = await request(app)
        .get('/api/v1/user/consumption-stats?period=today')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
    });

    test('无效时间段', async () => {
      const res = await request(app)
        .get('/api/v1/user/consumption-stats?period=invalid')
        .set(testUtils.getAuthHeader())
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/user/daily-consumption - 获取每日消费趋势', () => {
    test('获取每日消费趋势成功', async () => {
      const res = await request(app)
        .get('/api/v1/user/daily-consumption')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);
    });

    test('自定义天数', async () => {
      const res = await request(app)
        .get('/api/v1/user/daily-consumption?days=7')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
    });

    test('无效天数', async () => {
      const res = await request(app)
        .get('/api/v1/user/daily-consumption?days=0')
        .set(testUtils.getAuthHeader())
        .expect(400);

      expect(res.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/user/consumption-by-character - 按角色消费统计', () => {
    test('获取按角色消费统计成功', async () => {
      const res = await request(app)
        .get('/api/v1/user/consumption-by-character')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
      expect(Array.isArray(res.body.data)).toBe(true);
    });

    test('按日期范围统计', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const res = await request(app)
        .get(`/api/v1/user/consumption-by-character?start_date=${startDate}&end_date=${endDate}`)
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.code).toBe(0);
    });

    test('自定义限制数量', async () => {
      const res = await request(app)
        .get('/api/v1/user/consumption-by-character?limit=5')
        .set(testUtils.getAuthHeader())
        .expect(200);

      expect(res.body.data.length).toBeLessThanOrEqual(5);
    });
  });
});
