require('dotenv').config();

// AI服务配置
const aiConfig = {
  // OpenAI配置
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
    organization: process.env.OPENAI_ORGANIZATION || '',
    timeout: parseInt(process.env.OPENAI_TIMEOUT) || 30000,
    maxRetries: parseInt(process.env.OPENAI_MAX_RETRIES) || 3,
    
    // 默认模型配置
    defaultModel: process.env.OPENAI_DEFAULT_MODEL || 'DeepSeek-V3-Fast',

    // 支持的模型列表
    supportedModels: [
      'DeepSeek-V3-Fast',
      'DeepSeek-v3',
      'Qwen2.5-72B-Instruct',
      'Qwen2.5-32B-Instruct',
      'Qwen2.5-VL-72B-Instruct',
      'gpt-3.5-turbo',
      'gpt-4'
    ],
    
    // 模型价格配置 (每1000 tokens的价格，单位：人民币)
    modelPricing: {
      'DeepSeek-V3-Fast': {
        input: 0.002,  // ¥2/M tokens (限时五折)
        output: 0.008  // ¥8/M tokens (限时五折)
      },
      'DeepSeek-v3': {
        input: 0.002,
        output: 0.008
      },
      'Qwen2.5-72B-Instruct': {
        input: 0.003,
        output: 0.006
      },
      'Qwen2.5-32B-Instruct': {
        input: 0.002,
        output: 0.004
      },
      'gpt-3.5-turbo': {
        input: 0.0015,
        output: 0.002
      },
      'gpt-4': {
        input: 0.03,
        output: 0.06
      }
    }
  },
  
  // 对话配置
  conversation: {
    // 最大上下文长度
    maxContextLength: parseInt(process.env.MAX_CONTEXT_LENGTH) || 10,
    
    // 最大Token数限制
    maxTokens: parseInt(process.env.MAX_TOKENS) || 2000,
    
    // 温度参数
    temperature: parseFloat(process.env.TEMPERATURE) || 0.7,
    
    // Top-p参数
    topP: parseFloat(process.env.TOP_P) || 1.0,
    
    // 频率惩罚
    frequencyPenalty: parseFloat(process.env.FREQUENCY_PENALTY) || 0.0,
    
    // 存在惩罚
    presencePenalty: parseFloat(process.env.PRESENCE_PENALTY) || 0.0,
    
    // 流式响应
    stream: process.env.ENABLE_STREAM === 'true',
    
    // 安全过滤
    enableSafetyFilter: process.env.ENABLE_SAFETY_FILTER !== 'false'
  },
  
  // 余额和计费配置
  billing: {
    // Token到余额的转换比例 (1 token = ? 余额单位)
    tokenToBalanceRatio: parseFloat(process.env.TOKEN_TO_BALANCE_RATIO) || 1,

    // 美元到人民币汇率 (DeepSeek价格已经是人民币，设为1)
    usdToCnyRate: parseFloat(process.env.USD_TO_CNY_RATE) || 1.0,

    // 平台服务费率 (在API成本基础上的加成)
    serviceFeeRate: parseFloat(process.env.SERVICE_FEE_RATE) || 0.2, // 20%加成

    // 最小扣费金额
    minChargeAmount: parseFloat(process.env.MIN_CHARGE_AMOUNT) || 0.001
  },
  
  // 限流配置
  rateLimit: {
    // 每用户每分钟最大请求数
    requestsPerMinute: parseInt(process.env.AI_REQUESTS_PER_MINUTE) || 20,
    
    // 每用户每小时最大Token数
    tokensPerHour: parseInt(process.env.AI_TOKENS_PER_HOUR) || 100000,
    
    // 并发请求限制
    maxConcurrentRequests: parseInt(process.env.MAX_CONCURRENT_REQUESTS) || 5
  },
  
  // 缓存配置
  cache: {
    // 是否启用响应缓存
    enabled: process.env.ENABLE_AI_CACHE === 'true',
    
    // 缓存过期时间 (秒)
    ttl: parseInt(process.env.AI_CACHE_TTL) || 3600,
    
    // 缓存键前缀
    keyPrefix: 'ai_cache:'
  },
  
  // 安全配置
  security: {
    // 内容过滤关键词
    bannedKeywords: [
      '暴力', '色情', '政治敏感', '违法犯罪'
    ],
    
    // 最大输入长度
    maxInputLength: parseInt(process.env.MAX_INPUT_LENGTH) || 4000,
    
    // 启用内容审核
    enableContentModeration: process.env.ENABLE_CONTENT_MODERATION === 'true'
  }
};

// 验证配置
function validateConfig() {
  const errors = [];
  
  if (!aiConfig.openai.apiKey && process.env.NODE_ENV === 'production') {
    errors.push('OpenAI API Key is required in production');
  }
  
  if (aiConfig.conversation.maxTokens > 4096 && aiConfig.openai.defaultModel === 'gpt-3.5-turbo') {
    errors.push('Max tokens exceeds model limit for gpt-3.5-turbo');
  }
  
  if (aiConfig.conversation.temperature < 0 || aiConfig.conversation.temperature > 2) {
    errors.push('Temperature must be between 0 and 2');
  }
  
  if (errors.length > 0) {
    throw new Error(`AI Configuration errors: ${errors.join(', ')}`);
  }
}

// 获取模型价格
function getModelPricing(model) {
  return aiConfig.openai.modelPricing[model] || aiConfig.openai.modelPricing['gpt-3.5-turbo'];
}

// 计算Token成本
function calculateTokenCost(model, inputTokens, outputTokens) {
  const pricing = getModelPricing(model);
  const inputCost = (inputTokens / 1000) * pricing.input;
  const outputCost = (outputTokens / 1000) * pricing.output;
  return inputCost + outputCost;
}

// 计算用户应付费用 (包含服务费)
function calculateUserCost(apiCost) {
  const costInCny = apiCost * aiConfig.billing.usdToCnyRate;
  const totalCost = costInCny * (1 + aiConfig.billing.serviceFeeRate);
  return Math.max(totalCost, aiConfig.billing.minChargeAmount);
}

// 验证配置
if (process.env.NODE_ENV !== 'test') {
  try {
    validateConfig();
  } catch (error) {
    console.error('AI Configuration Error:', error.message);
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }
}

module.exports = {
  aiConfig,
  validateConfig,
  getModelPricing,
  calculateTokenCost,
  calculateUserCost
};
