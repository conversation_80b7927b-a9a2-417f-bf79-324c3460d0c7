const http = require('http');

console.log('🚀 开始API快速测试...\n');

// 测试健康检查
function testHealth() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3000/health', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          if (json.status === 'ok') {
            console.log('✅ 健康检查通过');
            resolve(true);
          } else {
            console.log('❌ 健康检查失败');
            resolve(false);
          }
        } catch (e) {
          console.log('❌ 健康检查响应解析失败');
          resolve(false);
        }
      });
    });
    req.on('error', (err) => {
      console.log('❌ 健康检查请求失败:', err.message);
      resolve(false);
    });
  });
}

// 测试AI角色列表
function testCharacters() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3000/api/v1/ai-characters', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          if (json.code === 0 && Array.isArray(json.data.list)) {
            console.log(`✅ AI角色列表获取成功 (${json.data.list.length}个角色)`);
            resolve(true);
          } else {
            console.log('❌ AI角色列表获取失败');
            console.log('响应:', json);
            resolve(false);
          }
        } catch (e) {
          console.log('❌ AI角色列表响应解析失败');
          resolve(false);
        }
      });
    });
    req.on('error', (err) => {
      console.log('❌ AI角色列表请求失败:', err.message);
      resolve(false);
    });
  });
}

// 测试充值套餐
function testPackages() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3000/api/v1/recharge-packages', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          if (json.code === 0 && Array.isArray(json.data)) {
            console.log(`✅ 充值套餐获取成功 (${json.data.length}个套餐)`);
            resolve(true);
          } else {
            console.log('❌ 充值套餐获取失败');
            console.log('响应:', json);
            resolve(false);
          }
        } catch (e) {
          console.log('❌ 充值套餐响应解析失败');
          resolve(false);
        }
      });
    });
    req.on('error', (err) => {
      console.log('❌ 充值套餐请求失败:', err.message);
      resolve(false);
    });
  });
}

// 测试用户注册
function testRegister() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: 'password123'
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          if (json.code === 0 && json.data.access_token) {
            console.log('✅ 用户注册成功');
            resolve(json.data.access_token);
          } else {
            console.log('❌ 用户注册失败');
            console.log('响应:', json);
            resolve(false);
          }
        } catch (e) {
          console.log('❌ 用户注册响应解析失败');
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log('❌ 用户注册请求失败:', err.message);
      resolve(false);
    });

    req.write(postData);
    req.end();
  });
}

// 测试获取用户信息
function testUserProfile(token) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/v1/user/profile',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          if (json.code === 0 && json.data.uid) {
            console.log('✅ 获取用户信息成功');
            resolve(true);
          } else {
            console.log('❌ 获取用户信息失败');
            console.log('响应:', json);
            resolve(false);
          }
        } catch (e) {
          console.log('❌ 用户信息响应解析失败');
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log('❌ 获取用户信息请求失败:', err.message);
      resolve(false);
    });

    req.end();
  });
}

// 运行所有测试
async function runAllTests() {
  let passed = 0;
  let total = 0;

  // 测试1: 健康检查
  total++;
  if (await testHealth()) passed++;

  // 测试2: AI角色列表
  total++;
  if (await testCharacters()) passed++;

  // 测试3: 充值套餐
  total++;
  if (await testPackages()) passed++;

  // 测试4: 用户注册
  total++;
  const token = await testRegister();
  if (token) passed++;

  // 测试5: 获取用户信息（需要token）
  if (token) {
    total++;
    if (await testUserProfile(token)) passed++;
  }

  // 输出结果
  console.log('\n' + '='.repeat(50));
  console.log('📊 测试结果摘要');
  console.log('='.repeat(50));
  console.log(`✅ 通过: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 所有测试都通过了！API已准备好交付给前端团队。');
  } else {
    console.log(`\n💥 ${total - passed} 个测试失败，请检查并修复问题。`);
  }
}

runAllTests().catch(console.error);
