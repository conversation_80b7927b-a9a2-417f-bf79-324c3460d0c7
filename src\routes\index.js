const express = require('express');
const router = express.Router();

// 导入各模块路由
const authRoutes = require('./auth');
const publicRoutes = require('./public');
const chatRoutes = require('./chat');
const userRoutes = require('./user');
const paymentRoutes = require('./payment');
const aiRoutes = require('./ai');

// API版本前缀
const API_VERSION = '/api/v1';

// 认证模块路由
router.use(`${API_VERSION}/auth`, authRoutes);

// 公开接口路由（无需认证）
router.use(API_VERSION, publicRoutes);

// 聊天模块路由
router.use(`${API_VERSION}/chat`, chatRoutes);

// 用户中心路由
router.use(`${API_VERSION}/user`, userRoutes);

// 支付模块路由
router.use(`${API_VERSION}/payment`, paymentRoutes);

// AI服务路由
router.use(`${API_VERSION}/ai`, aiRoutes);

// 健康检查接口
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API文档接口（开发环境）
router.get(`${API_VERSION}/docs`, (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ message: 'Not found' });
  }

  const apiDocs = {
    title: '树洞聊天系统 API 文档',
    version: '1.0.0',
    baseUrl: `${req.protocol}://${req.get('host')}${API_VERSION}`,
    endpoints: {
      auth: {
        'POST /auth/register': '用户注册',
        'POST /auth/login': '用户登录',
        'POST /auth/logout': '用户登出',
        'POST /auth/refresh': '刷新Token',
        'GET /auth/me': '获取当前用户信息',
        'POST /auth/send-code': '发送验证码'
      },
      public: {
        'GET /ai-characters': '获取AI角色列表',
        'GET /ai-characters/:id': '获取AI角色详情',
        'GET /ai-characters/popular': '获取热门角色',
        'GET /ai-characters/search': '搜索角色',
        'GET /recharge-packages': '获取充值套餐',
        'GET /recharge-packages/recommended': '获取推荐套餐',
        'GET /stats': '获取网站统计'
      },
      chat: {
        'POST /chat/messages': '发送消息',
        'GET /chat/sessions/:character_id/messages': '获取历史消息',
        'GET /chat/sessions': '获取会话列表',
        'DELETE /chat/sessions/:session_id': '删除会话',
        'GET /chat/sessions/stats': '获取会话统计'
      },
      user: {
        'GET /user/profile': '获取用户信息',
        'PUT /user/password': '修改密码',
        'GET /user/consumption-logs': '获取消费记录',
        'GET /user/recharge-orders': '获取充值记录',
        'GET /user/stats': '获取用户统计'
      },
      payment: {
        'POST /payment/orders': '创建充值订单',
        'GET /payment/orders/:order_sn': '查询订单状态',
        'GET /payment/methods': '获取支付方式',
        'POST /payment/notify/alipay': '支付宝回调',
        'POST /payment/notify/wechat_pay': '微信支付回调'
      },
      ai: {
        'GET /ai/health': 'AI服务健康检查',
        'GET /ai/models': '获取可用模型列表',
        'GET /ai/pricing': '获取模型定价信息',
        'POST /ai/test': '测试AI对话(开发环境)',
        'GET /ai/stats': '获取AI服务统计',
        'GET /ai/user-stats': '获取用户AI使用统计'
      }
    },
    authentication: {
      type: 'Bearer Token',
      header: 'Authorization: Bearer <token>',
      description: '除公开接口外，所有接口都需要在请求头中携带JWT Token'
    },
    responseFormat: {
      success: {
        code: 0,
        message: 'Success',
        data: '业务数据'
      },
      error: {
        code: '错误码',
        message: '错误信息',
        data: null
      }
    }
  };

  res.json(apiDocs);
});

module.exports = router;
