const BaseModel = require('./BaseModel');

class UserPreference extends BaseModel {
  constructor() {
    super('user_preferences');
  }

  /**
   * 获取用户偏好
   * @param {Number} userId - 用户ID
   * @param {String} preferenceKey - 偏好键
   * @returns {Object|null} 偏好对象
   */
  async getUserPreference(userId, preferenceKey) {
    const sql = `
      SELECT * FROM ${this.tableName}
      WHERE user_id = ? AND preference_key = ?
      ORDER BY updated_at DESC
      LIMIT 1
    `;
    
    const results = await this.query(sql, [userId, preferenceKey]);
    return results.length > 0 ? results[0] : null;
  }

  /**
   * 设置用户偏好
   * @param {Number} userId - 用户ID
   * @param {String} preferenceKey - 偏好键
   * @param {*} preferenceValue - 偏好值
   * @param {String} preferenceType - 偏好类型
   * @param {Number} weight - 权重
   * @returns {Object} 创建或更新的偏好对象
   */
  async setUserPreference(userId, preferenceKey, preferenceValue, preferenceType = 'string', weight = 1.0) {
    // 处理不同类型的值
    let processedValue = preferenceValue;
    if (preferenceType === 'json') {
      processedValue = JSON.stringify(preferenceValue);
    } else if (preferenceType === 'boolean') {
      processedValue = preferenceValue ? '1' : '0';
    } else if (preferenceType === 'number') {
      processedValue = String(preferenceValue);
    }

    const sql = `
      INSERT INTO ${this.tableName} 
      (user_id, preference_key, preference_value, preference_type, weight)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
      preference_value = VALUES(preference_value),
      preference_type = VALUES(preference_type),
      weight = VALUES(weight),
      updated_at = CURRENT_TIMESTAMP
    `;

    await this.query(sql, [userId, preferenceKey, processedValue, preferenceType, weight]);
    return await this.getUserPreference(userId, preferenceKey);
  }

  /**
   * 获取用户所有偏好
   * @param {Number} userId - 用户ID
   * @param {Array} categories - 偏好分类过滤
   * @returns {Array} 偏好列表
   */
  async getUserPreferences(userId, categories = null) {
    let sql = `
      SELECT preference_key, preference_value, preference_type, weight, updated_at
      FROM ${this.tableName}
      WHERE user_id = ?
    `;

    const params = [userId];

    if (categories && Array.isArray(categories) && categories.length > 0) {
      const placeholders = categories.map(() => '?').join(',');
      sql += ` AND preference_key IN (${placeholders})`;
      params.push(...categories);
    }

    sql += ` ORDER BY weight DESC, updated_at DESC`;

    const results = await this.query(sql, params);
    
    // 处理不同类型的值
    return results.map(pref => ({
      ...pref,
      preference_value: this.parsePreferenceValue(pref.preference_value, pref.preference_type)
    }));
  }

  /**
   * 解析偏好值
   * @param {String} value - 原始值
   * @param {String} type - 类型
   * @returns {*} 解析后的值
   */
  parsePreferenceValue(value, type) {
    switch (type) {
      case 'json':
        try {
          return JSON.parse(value);
        } catch (e) {
          return value;
        }
      case 'boolean':
        return value === '1' || value === 'true';
      case 'number':
        return parseFloat(value);
      default:
        return value;
    }
  }

  /**
   * 删除用户偏好
   * @param {Number} userId - 用户ID
   * @param {String} preferenceKey - 偏好键
   * @returns {Boolean} 是否删除成功
   */
  async deleteUserPreference(userId, preferenceKey) {
    const sql = `
      DELETE FROM ${this.tableName}
      WHERE user_id = ? AND preference_key = ?
    `;
    
    const result = await this.query(sql, [userId, preferenceKey]);
    return result.affectedRows > 0;
  }

  /**
   * 批量设置用户偏好
   * @param {Number} userId - 用户ID
   * @param {Object} preferences - 偏好对象
   * @returns {Array} 设置结果
   */
  async setBatchPreferences(userId, preferences) {
    const results = [];
    
    for (const [key, config] of Object.entries(preferences)) {
      const { value, type = 'string', weight = 1.0 } = config;
      const result = await this.setUserPreference(userId, key, value, type, weight);
      results.push(result);
    }
    
    return results;
  }

  /**
   * 获取偏好统计
   * @param {Number} userId - 用户ID
   * @returns {Object} 统计信息
   */
  async getPreferenceStats(userId) {
    const sql = `
      SELECT 
        COUNT(*) as total_preferences,
        AVG(weight) as avg_weight,
        MAX(updated_at) as last_updated,
        preference_type,
        COUNT(*) as type_count
      FROM ${this.tableName}
      WHERE user_id = ?
      GROUP BY preference_type
    `;
    
    const results = await this.query(sql, [userId]);
    
    const totalSql = `
      SELECT COUNT(*) as total FROM ${this.tableName} WHERE user_id = ?
    `;
    const totalResult = await this.query(totalSql, [userId]);
    
    return {
      total_preferences: totalResult[0].total,
      type_distribution: results,
      last_updated: results.length > 0 ? Math.max(...results.map(r => new Date(r.last_updated))) : null
    };
  }

  /**
   * 清理过期偏好
   * @param {Number} userId - 用户ID
   * @param {Number} daysOld - 多少天前的偏好
   * @returns {Number} 清理数量
   */
  async cleanupOldPreferences(userId, daysOld = 90) {
    const sql = `
      DELETE FROM ${this.tableName}
      WHERE user_id = ? 
      AND updated_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      AND weight < 0.3
    `;
    
    const result = await this.query(sql, [userId, daysOld]);
    return result.affectedRows;
  }

  /**
   * 搜索偏好
   * @param {Number} userId - 用户ID
   * @param {String} keyword - 搜索关键词
   * @returns {Array} 匹配的偏好
   */
  async searchPreferences(userId, keyword) {
    const sql = `
      SELECT * FROM ${this.tableName}
      WHERE user_id = ? 
      AND (preference_key LIKE ? OR preference_value LIKE ?)
      ORDER BY weight DESC, updated_at DESC
    `;
    
    const searchPattern = `%${keyword}%`;
    const results = await this.query(sql, [userId, searchPattern, searchPattern]);
    
    return results.map(pref => ({
      ...pref,
      preference_value: this.parsePreferenceValue(pref.preference_value, pref.preference_type)
    }));
  }
}

module.exports = new UserPreference();
