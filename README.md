# 树洞聊天系统后端 API

基于 Node.js + Express + MySQL 的树洞聊天系统后端服务，为用户提供与AI角色对话的平台。

## 🚀 功能特性

### 第一部分：前台功能（已实现接口预留）

#### 1. 核心对话模块
- ✅ AI角色选择和展示
- ✅ 实时对话接口
- ✅ 历史消息查询
- ✅ 会话管理
- ✅ 余额消耗计算

#### 2. 用户账户模块
- ✅ 用户注册/登录
- ✅ JWT认证机制
- ✅ 个人中心信息
- ✅ 密码修改
- ✅ 消费记录查询

#### 3. 内容与展示模块
- ✅ 首页数据接口
- ✅ AI角色详情
- ✅ 充值套餐展示
- ✅ 搜索功能

#### 4. 支付模块
- ✅ 充值订单创建
- ✅ 支付状态查询
- ✅ 支付回调处理
- ✅ 余额管理

## 📋 技术栈

- **后端框架**: Node.js + Express.js
- **数据库**: MySQL 8.0+
- **认证**: JWT (JSON Web Token)
- **参数验证**: Joi
- **安全**: Helmet + CORS + Rate Limiting
- **密码加密**: bcryptjs

## 🛠️ 安装和运行

### 环境要求
- Node.js 16+
- MySQL 8.0+
- npm 或 yarn

### 1. 克隆项目
```bash
git clone <repository-url>
cd tree-hole-backend
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
复制 `.env` 文件并修改配置：
```bash
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=tree_hole_chat

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
```

### 4. 初始化数据库
```bash
# 启动服务器会自动创建数据库和表
npm run dev

# 或者手动执行初始化数据
mysql -u root -p < init_data.sql
```

### 5. 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

服务器启动后访问：
- API服务: http://localhost:3000
- API文档: http://localhost:3000/api/v1/docs
- 健康检查: http://localhost:3000/health

## 📚 API 文档

### 认证接口 (`/api/v1/auth`)
- `POST /register` - 用户注册
- `POST /login` - 用户登录
- `POST /logout` - 用户登出
- `GET /me` - 获取当前用户信息

### 公开接口 (`/api/v1`)
- `GET /ai-characters` - 获取AI角色列表
- `GET /ai-characters/:id` - 获取AI角色详情
- `GET /recharge-packages` - 获取充值套餐

### 聊天接口 (`/api/v1/chat`) 🔒
- `POST /messages` - 发送消息并获取AI回复
- `GET /sessions/:character_id/messages` - 获取历史消息
- `GET /sessions` - 获取会话列表

### 用户中心 (`/api/v1/user`) 🔒
- `GET /profile` - 获取用户信息
- `PUT /password` - 修改密码
- `GET /consumption-logs` - 获取消费记录
- `GET /recharge-orders` - 获取充值记录

### 支付接口 (`/api/v1/payment`)
- `POST /orders` - 创建充值订单 🔒
- `GET /orders/:order_sn` - 查询订单状态 🔒
- `POST /notify/alipay` - 支付宝回调
- `POST /notify/wechat_pay` - 微信支付回调

🔒 表示需要JWT认证

## 🗄️ 数据库设计

### 核心表结构
- `users` - 用户表
- `ai_characters` - AI角色表
- `chat_sessions` - 聊天会话表
- `chat_messages` - 聊天消息表
- `consumption_logs` - 消费日志表
- `recharge_packages` - 充值套餐表
- `recharge_orders` - 充值订单表
- `system_configs` - 系统配置表

详细的数据库设计请参考 `数据库设计.txt` 文件。

## 🔧 配置说明

### 系统配置
系统支持动态配置，主要配置项：
- `token_to_balance_ratio` - Token与余额的兑换比例
- `new_user_bonus` - 新用户注册赠送余额
- `balance_to_rmb_ratio` - 余额与人民币的兑换比例

### 安全配置
- JWT密钥配置
- 请求频率限制
- CORS跨域配置
- 安全头设置

## 📊 监控和日志

- 请求日志记录
- 错误日志记录
- 性能监控（预留）
- 健康检查接口

## 🚧 待实现功能

### AI服务集成
- [ ] OpenAI API集成
- [ ] 流式响应支持
- [ ] Token使用量统计
- [ ] 多模型支持

### 高级功能
- [ ] 长期记忆系统
- [ ] 角色动态事件
- [ ] 用户行为分析
- [ ] 推荐系统

### 运维功能
- [ ] 日志系统完善
- [ ] 监控告警
- [ ] 数据备份
- [ ] 性能优化

## 🤝 开发规范

### 代码结构
```
src/
├── config/         # 配置文件
├── controllers/    # 控制器
├── middleware/     # 中间件
├── models/         # 数据模型
├── routes/         # 路由配置
├── utils/          # 工具类
└── app.js          # 主应用文件
```

### 响应格式
```json
{
  "code": 0,
  "message": "Success",
  "data": {}
}
```

### 错误码规范
- `0` - 成功
- `4xxxx` - 客户端错误
- `5xxxx` - 服务端错误

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 完成第一部分接口预留
- ✅ 实现用户认证系统
- ✅ 实现基础聊天功能
- ✅ 实现充值支付系统
- ✅ 完成数据库设计

## 📄 许可证

MIT License

## 👥 贡献者

- YZFly - 项目架构师

---

**注意**: 这是第一部分的实现，主要完成了接口预留和基础架构。AI服务集成、支付接口对接等功能需要在后续版本中完善。
